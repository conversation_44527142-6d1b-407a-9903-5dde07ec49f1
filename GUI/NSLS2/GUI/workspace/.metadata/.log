!SESSION 2025-05-01 12:00:08.797 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.26
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

This is a continuation of log file /epics/GUI/NSLS2/GUI/workspace/.metadata/.bak_0.log
Created Time: 2025-05-01 12:03:31.813

!ENTRY org.eclipse.egit.core 1 0 2025-05-01 12:03:31.813
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-05-01 12:03:32.155
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-05-01 12:03:32.155
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@44ddb518,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@e36bc01,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-05-01 12:03:34.494
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-05-01 12:07:25.113
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at jdk.internal.reflect.GeneratedMethodAccessor6.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 24 more
!SESSION 2025-05-02 11:57:25.715 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.26
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-05-02 11:57:26.713
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:108)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:183)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:147)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:271)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:147)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 62 more

!ENTRY org.python.jython 4 0 2025-05-02 11:57:26.715
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:108)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:183)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:147)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:271)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:147)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 66 more

!ENTRY org.python.jython 4 0 2025-05-02 11:57:26.717
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:108)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:183)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:147)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:271)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:147)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 66 more

!ENTRY org.python.jython 4 0 2025-05-02 11:57:26.718
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:108)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:183)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:147)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:271)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:147)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 66 more

!ENTRY org.python.jython 4 0 2025-05-02 11:57:26.912
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at jdk.internal.reflect.GeneratedMethodAccessor7.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1776)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1275)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1741)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1470)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1433)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:743)
	at java.base/java.util.zip.ZipFile$CleanableResource.get(ZipFile.java:860)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:258)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:187)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:201)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 68 more

!ENTRY org.eclipse.egit.core 1 0 2025-05-02 11:57:30.761
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-05-02 11:57:31.073
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-05-02 11:57:31.073
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@e36bc01,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6654ca0d,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-05-02 11:57:33.769
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2025-06-10 12:18:47.226 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-10 12:18:48.479
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-10 12:18:48.482
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-10 12:18:48.483
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-10 12:18:48.685
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.python.jython 4 0 2025-06-10 12:18:48.862
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at org.eclipse.core.internal.registry.osgi.EclipseBundleListener.getExtensionURL(EclipseBundleListener.java:122)
	at org.eclipse.core.internal.registry.osgi.EclipseBundleListener.addBundle(EclipseBundleListener.java:165)
	at org.eclipse.core.internal.registry.osgi.EclipseBundleListener.processBundles(EclipseBundleListener.java:94)
	at org.eclipse.core.internal.registry.osgi.RegistryStrategyOSGI.onStart(RegistryStrategyOSGI.java:243)
	at org.eclipse.core.internal.registry.ExtensionRegistry.<init>(ExtensionRegistry.java:740)
	at org.eclipse.core.runtime.RegistryFactory.createRegistry(RegistryFactory.java:61)
	at org.eclipse.core.internal.registry.osgi.Activator.startRegistry(Activator.java:143)
	at org.eclipse.core.internal.registry.osgi.Activator.start(Activator.java:60)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:814)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:806)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:763)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1011)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:365)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:506)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:117)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:572)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:346)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:398)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:41)
	at org.eclipse.osgi.internal.loader.sources.MultiSourcePackage.loadClass(MultiSourcePackage.java:37)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:473)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3549)
	at java.base/java.lang.Class.getConstructors(Class.java:2227)
	at org.apache.felix.scr.impl.inject.internal.ComponentConstructorImpl.<init>(ComponentConstructorImpl.java:97)
	at org.apache.felix.scr.impl.inject.internal.ComponentMethodsImpl.initComponentMethods(ComponentMethodsImpl.java:110)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.initDependencyManagers(AbstractComponentManager.java:1036)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.collectDependencies(AbstractComponentManager.java:1054)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:953)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:776)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 76 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-10 12:18:53.454
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-10 12:18:53.765
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-10 12:18:53.765
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@37f7ce20,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@c931728,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-10 12:18:56.083
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2025-06-11 11:27:51.267 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-11 11:27:52.175
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-11 11:27:52.177
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-11 11:27:52.178
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-11 11:27:52.178
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-11 11:27:52.394
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-11 11:27:56.081
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-11 11:27:56.377
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-11 11:27:56.377
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@5f3f57ff,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6200b644,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-11 11:27:58.699
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-11 11:31:11.947
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more
!SESSION 2025-06-12 14:18:22.981 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-12 14:18:24.115
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-12 14:18:24.118
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-12 14:18:24.119
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-12 14:18:24.119
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-12 14:18:24.360
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-12 14:18:29.089
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-12 14:18:29.448
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-12 14:18:29.448
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1b1ea1d9,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6a902015,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-12 14:18:32.410
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-12 14:38:54.461
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more
!SESSION 2025-06-16 14:37:39.539 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-16 14:37:40.580
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-16 14:37:40.583
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-16 14:37:40.584
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-16 14:37:40.584
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-16 14:37:40.815
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-16 14:37:44.600
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-16 14:37:44.939
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-16 14:37:44.939
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@4e2824b1,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@534d0e20,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.python.jython 4 0 2025-06-16 14:37:47.719
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-16 14:37:48.215
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2025-06-20 14:48:12.850 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-20 14:48:13.707
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-20 14:48:13.709
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:48:13.709
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:48:13.710
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:48:13.882
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-20 14:48:17.487
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-20 14:48:17.776
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-20 14:48:17.776
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@521a506c,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@3c64339f,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-20 14:48:19.904
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2025-06-20 14:49:52.211 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-20 14:49:52.971
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-20 14:49:52.973
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:49:52.974
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:49:52.974
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-20 14:49:53.134
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-20 14:49:56.299
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-20 14:49:56.594
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-20 14:49:56.594
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6a902015,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@55d99dc3,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-20 14:49:58.851
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2025-06-23 21:15:57.335 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-23 21:15:58.224
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-23 21:15:58.226
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:15:58.228
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:15:58.228
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:15:58.396
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-23 21:16:01.688
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-23 21:16:01.977
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-23 21:16:01.977
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@59f76e56,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@566f4659,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-23 21:16:04.311
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-23 21:30:25.038
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.csstudio.autocomplete.pvmanager.formula.FormulaFunctionProvider.<init>(FormulaFunctionProvider.java:39)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.apache.felix.scr.impl.inject.internal.ComponentConstructorImpl.newInstance(ComponentConstructorImpl.java:312)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:286)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:918)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse$1.run(ServiceFactoryUse.java:216)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:319)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse.factoryGetService(ServiceFactoryUse.java:213)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse.getService(ServiceFactoryUse.java:114)
	at org.eclipse.osgi.internal.serviceregistry.ServiceConsumer$2.getService(ServiceConsumer.java:48)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.getService(ServiceRegistrationImpl.java:547)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.getService(ServiceRegistry.java:533)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.getService(BundleContextImpl.java:656)
	at org.csstudio.autocomplete.AutoCompleteService.getOSGIProviders(AutoCompleteService.java:250)
	at org.csstudio.autocomplete.AutoCompleteService.<init>(AutoCompleteService.java:155)
	at org.csstudio.autocomplete.AutoCompleteService.getInstance(AutoCompleteService.java:171)
	at org.csstudio.autocomplete.ui.AutoCompleteProposalProvider.cancel(AutoCompleteProposalProvider.java:105)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter.getProposals(ContentProposalAdapter.java:596)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter.openProposalPopup(ContentProposalAdapter.java:509)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter$3.run(ContentProposalAdapter.java:455)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:185)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:5023)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:4529)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1157)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:338)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:644)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:338)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:551)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:156)
	at org.csstudio.utility.product.Workbench.runWorkbench(Workbench.java:99)
	at org.csstudio.startup.application.Application.startApplication(Application.java:265)
	at org.csstudio.startup.application.Application.start(Application.java:119)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:203)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:401)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:653)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:590)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1461)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1434)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more
!SESSION 2025-06-23 21:50:10.407 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-23 21:50:10.985
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-23 21:50:10.987
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:50:10.988
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:50:10.988
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-23 21:50:11.119
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-23 21:50:15.804
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-23 21:50:16.075
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-23 21:50:16.075
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1a3b1f7e,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@22899683,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-23 21:50:18.356
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-23 22:07:21.700
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.csstudio.autocomplete.pvmanager.formula.FormulaFunctionProvider.<init>(FormulaFunctionProvider.java:39)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.apache.felix.scr.impl.inject.internal.ComponentConstructorImpl.newInstance(ComponentConstructorImpl.java:312)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:286)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:918)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse$1.run(ServiceFactoryUse.java:216)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:319)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse.factoryGetService(ServiceFactoryUse.java:213)
	at org.eclipse.osgi.internal.serviceregistry.ServiceFactoryUse.getService(ServiceFactoryUse.java:114)
	at org.eclipse.osgi.internal.serviceregistry.ServiceConsumer$2.getService(ServiceConsumer.java:48)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.getService(ServiceRegistrationImpl.java:547)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.getService(ServiceRegistry.java:533)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.getService(BundleContextImpl.java:656)
	at org.csstudio.autocomplete.AutoCompleteService.getOSGIProviders(AutoCompleteService.java:250)
	at org.csstudio.autocomplete.AutoCompleteService.<init>(AutoCompleteService.java:155)
	at org.csstudio.autocomplete.AutoCompleteService.getInstance(AutoCompleteService.java:171)
	at org.csstudio.autocomplete.ui.AutoCompleteProposalProvider.cancel(AutoCompleteProposalProvider.java:105)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter.getProposals(ContentProposalAdapter.java:596)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter.openProposalPopup(ContentProposalAdapter.java:509)
	at org.csstudio.autocomplete.ui.content.ContentProposalAdapter$3.run(ContentProposalAdapter.java:455)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:185)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:5023)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:4529)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1157)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:338)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:644)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:338)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:551)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:156)
	at org.csstudio.utility.product.Workbench.runWorkbench(Workbench.java:99)
	at org.csstudio.startup.application.Application.startApplication(Application.java:265)
	at org.csstudio.startup.application.Application.start(Application.java:119)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:203)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:401)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:653)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:590)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1461)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1434)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more
!SESSION 2025-06-24 12:59:31.119 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-24 12:59:31.738
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-24 12:59:31.740
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-24 12:59:31.742
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-24 12:59:31.743
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-24 12:59:31.889
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-24 12:59:34.553
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-24 12:59:34.806
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-24 12:59:34.806
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@22899683,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@40017e98,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-24 12:59:37.094
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-24 13:10:38.733
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more
!SESSION 2025-06-25 10:25:41.379 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-25 10:25:42.361
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-25 10:25:42.365
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-25 10:25:42.366
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-25 10:25:42.367
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-25 10:25:42.562
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-25 10:25:49.197
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-25 10:25:49.534
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-25 10:25:49.534
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1a3b1f7e,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@22899683,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-25 10:25:52.405
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled

!ENTRY org.python.jython 4 0 2025-06-25 11:23:20.110
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more
!SESSION 2025-06-26 12:38:38.040 -----------------------------------------------
eclipse.buildId=unknown
java.version=21.0.7
java.vendor=Ubuntu
BootLoader constants: OS=linux, ARCH=x86_64, WS=gtk, NL=en_US
Command-line arguments:  -os linux -ws gtk -arch x86_64

!ENTRY org.python.jython 4 0 2025-06-26 12:38:39.122
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getRawHeaders(BundleInfo.java:147)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:625)
	at org.eclipse.osgi.storage.BundleInfo$CachedManifest.get(BundleInfo.java:1)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:128)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 63 more

!ENTRY org.python.jython 4 0 2025-06-26 12:38:39.126
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-26 12:38:39.127
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-26 12:38:39.128
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2090)
	at org.eclipse.osgi.storage.Storage.findEntries(Storage.java:2013)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findEntries(ClasspathManager.java:879)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findEntries(ModuleClassLoader.java:395)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:852)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:292)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:213)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:144)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:115)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:188)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:514)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:503)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getDeployedJavaModuleBundlePaths(FXClassLoader.java:610)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.getModuleLayer(FXClassLoader.java:262)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.findClassJavaFX11(FXClassLoader.java:207)
	at org.eclipse.fx.osgi.fxloader.FXClassLoader.postFindClass(FXClassLoader.java:144)
	at org.eclipse.osgi.internal.loader.BundleLoader.searchHooks(BundleLoader.java:532)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:494)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:171)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at org.eclipse.osgi.internal.framework.ContextFinder.loadClass(ContextFinder.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at java.xml/javax.xml.parsers.FactoryFinder.getProviderClass(FactoryFinder.java:95)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:171)
	at java.xml/javax.xml.parsers.FactoryFinder.newInstance(FactoryFinder.java:134)
	at java.xml/javax.xml.parsers.FactoryFinder.find(FactoryFinder.java:238)
	at java.xml/javax.xml.parsers.SAXParserFactory.newInstance(SAXParserFactory.java:181)
	at org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(BundleComponentActivator.java:392)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialize(BundleComponentActivator.java:278)
	at org.apache.felix.scr.impl.BundleComponentActivator.<init>(BundleComponentActivator.java:213)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:552)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:488)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerModified(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:232)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1781)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 67 more

!ENTRY org.python.jython 4 0 2025-06-26 12:38:39.507
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.DataSourceProvider.createDataSource(DataSourceProvider.java:54)
	at org.diirt.datasource.PVManager.<clinit>(PVManager.java:50)
	at org.csstudio.diirt.util.RegisterDatasource.registerDatasource(RegisterDatasource.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invokeMethod(BaseMethod.java:244)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.access$500(BaseMethod.java:41)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod$Resolved.invoke(BaseMethod.java:685)
	at org.apache.felix.scr.impl.inject.methods.BaseMethod.invoke(BaseMethod.java:529)
	at org.apache.felix.scr.impl.inject.methods.BindMethod.invoke(BindMethod.java:42)
	at org.apache.felix.scr.impl.manager.DependencyManager.doInvokeBindMethod(DependencyManager.java:2072)
	at org.apache.felix.scr.impl.manager.DependencyManager.bindDependency(DependencyManager.java:1895)
	at org.apache.felix.scr.impl.manager.DependencyManager.bind(DependencyManager.java:1882)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createImplementationObject(SingleComponentManager.java:320)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.createComponent(SingleComponentManager.java:115)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getService(SingleComponentManager.java:1000)
	at org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(SingleComponentManager.java:973)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal(AbstractComponentManager.java:785)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal(AbstractComponentManager.java:674)
	at org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(AbstractComponentManager.java:437)
	at org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(ConfigurableComponentHolder.java:667)
	at org.apache.felix.scr.impl.BundleComponentActivator.initialEnable(BundleComponentActivator.java:305)
	at org.apache.felix.scr.impl.Activator.loadComponents(Activator.java:554)
	at org.apache.felix.scr.impl.Activator.access$200(Activator.java:70)
	at org.apache.felix.scr.impl.Activator$ScrExtension.start(Activator.java:421)
	at org.apache.felix.scr.impl.AbstractExtender.createExtension(AbstractExtender.java:196)
	at org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(AbstractExtender.java:169)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:139)
	at org.apache.felix.scr.impl.AbstractExtender.addingBundle(AbstractExtender.java:49)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:475)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(BundleTracker.java:450)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:945)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(EquinoxEventPublisher.java:232)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:138)
	at org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(EquinoxEventPublisher.java:130)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(EquinoxContainerAdaptor.java:217)
	at org.eclipse.osgi.container.Module.publishEvent(Module.java:499)
	at org.eclipse.osgi.container.Module.start(Module.java:486)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1845)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1838)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1779)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1743)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1665)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 65 more

!ENTRY org.eclipse.egit.core 1 0 2025-06-26 12:38:43.126
!MESSAGE Using Apache MINA sshd as ssh client.

!ENTRY org.eclipse.jface 2 0 2025-06-26 12:38:43.615
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-06-26 12:38:43.615
!MESSAGE A conflict occurred for F11:
Binding(F11,
	ParameterizedCommand(Command(org.csstudio.opibuilder.actions.fullscreen,Full Screen,
		Enter/Exit Full Screen,
		Category(org.eclipse.ui.category.views,Views,Commands for opening views,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@428eb3d5,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(F11,
	ParameterizedCommand(Command(org.eclipse.debug.ui.commands.DebugLast,Debug,
		Launch in debug mode,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@5ddd84d2,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)

!ENTRY org.python.jython 4 0 2025-06-26 12:38:47.281
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: /epics/GUI/NSLS2/GUI/cs-studio/plugins/org.python.jython_2.7.1.release.jar
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:349)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:48)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.open(CloseableBundleFile.java:135)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:77)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:271)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getEntry(BundleInfo.java:423)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getEntry(EquinoxBundle.java:697)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.getEntry(ServiceLoaderOSGiWrapper.java:190)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper$OSGiReflection.access$500(ServiceLoaderOSGiWrapper.java:113)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.loadOSGi(ServiceLoaderOSGiWrapper.java:85)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:57)
	at org.diirt.util.config.ServiceLoaderOSGiWrapper.load(ServiceLoaderOSGiWrapper.java:43)
	at org.diirt.datasource.formula.FormulaRegistry.<clinit>(FormulaRegistry.java:29)
	at org.diirt.datasource.formula.ExpressionLanguage.function(ExpressionLanguage.java:185)
	at org.diirt.datasource.formula.FormulaAst.toExpression(FormulaAst.java:341)
	at org.diirt.datasource.formula.ExpressionLanguage.parseFormula(ExpressionLanguage.java:87)
	at org.diirt.datasource.formula.ExpressionLanguage.formula(ExpressionLanguage.java:122)
	at org.csstudio.simplepv.pvmanager.PVManagerPV.internalStart(PVManagerPV.java:244)
	at org.csstudio.simplepv.pvmanager.PVManagerPV$5.run(PVManagerPV.java:363)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.zip.ZipException: Invalid CEN header (invalid extra data field size for tag: 0xbfef at 130358)
	at java.base/java.util.zip.ZipFile$Source.zerror(ZipFile.java:1781)
	at java.base/java.util.zip.ZipFile$Source.checkExtraFields(ZipFile.java:1294)
	at java.base/java.util.zip.ZipFile$Source.checkAndAddEntry(ZipFile.java:1233)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1720)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1495)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1458)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:724)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:251)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:180)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:194)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:335)
	... 23 more

!ENTRY org.eclipse.e4.ui.workbench 2 0 2025-06-26 12:38:48.293
!MESSAGE Removing PartDescriptorImpl with the "org.csstudio.pretune" id and the "pretune" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
