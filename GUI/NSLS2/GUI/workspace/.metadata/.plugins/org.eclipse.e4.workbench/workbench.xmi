<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_bzGlIFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_bzHMoVXtEfCMzZJvQuNmkQ" bindingContexts="_bzHQM1XtEfCMzZJvQuNmkQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServalCtrl.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;pmacStatus.opi&quot; tooltip=&quot;cs-studio-xf/common/motor/pmacStatus.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/common/motor/pmacStatus.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main.opi&quot; tooltip=&quot;cs-studio-xf/10id/main.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/10id/main.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Detector.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Status.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Status.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Status.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Alarm.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Mask.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;MaskStatus.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3API.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3API.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3API.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Dashboard.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Measurement/Dashboard.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Measurement/Dashboard.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;WriteFiles.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;test.opi&quot; tooltip=&quot;cs-studio-xf/Mobile/Test/test.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main2.opi&quot; tooltip=&quot;cs-studio-xf/10id/main2.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/10id/main2.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;DetectorConfig.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServerFileWriter.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorVoltages.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorHealth.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorHealth.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorHealth.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorConfig.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorConfig.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorConfig.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorChip.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorChip.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorChip.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_bzGlIVXtEfCMzZJvQuNmkQ" selectedElement="_bzGlIlXtEfCMzZJvQuNmkQ" x="76" y="270" width="1995" height="1118">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlIlXtEfCMzZJvQuNmkQ" selectedElement="_bzGlI1XtEfCMzZJvQuNmkQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_bzGlI1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_bzGlVlXtEfCMzZJvQuNmkQ">
        <children xsi:type="advanced:Perspective" xmi:id="_bzGlJFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" selectedElement="_bzGlJVXtEfCMzZJvQuNmkQ" label="&lt;OPI Runtime>" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlJVXtEfCMzZJvQuNmkQ" selectedElement="_bzGlKVXtEfCMzZJvQuNmkQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_bzGlJlXtEfCMzZJvQuNmkQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzGlJ1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_bzGmQVXtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzGlKFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_bzGmV1XtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlKVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlKlXtEfCMzZJvQuNmkQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlKlXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlLVXtEfCMzZJvQuNmkQ">
                <children xsi:type="basic:PartStack" xmi:id="_bzGlK1XtEfCMzZJvQuNmkQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzGlLFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_bzGmQ1XtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlLVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlLlXtEfCMzZJvQuNmkQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlLlXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlMFXtEfCMzZJvQuNmkQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzGlL1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" containerData="1000" ref="_bzGmNVXtEfCMzZJvQuNmkQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_bzGlMFXtEfCMzZJvQuNmkQ" elementId="DEFAULT_VIEW" containerData="9000" selectedElement="_bzGlMVXtEfCMzZJvQuNmkQ">
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlMVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" ref="_bzGmbVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlMlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_bzGmRVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlM1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" ref="_bzGmcFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlNFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" ref="_bzGmc1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlNVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" toBeRendered="false" ref="_bzGmdlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlNlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:4f79fe78-23be-409b-a914-8e9e2f78279f" toBeRendered="false" ref="_bzGmfFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlN1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:cf37b73e-6960-4c76-bc87-7e7b27f51f48" ref="_bzGmf1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlOFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:f6baf561-82d2-46bd-abc6-fb8c3e7fdbaa" ref="_bzGmglXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlOVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:7f17f5b6-d606-41a8-a8e5-c402cb0cd5c0" ref="_bzGmmFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlOlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:0573e57f-864d-404c-9fca-423e5562252f" ref="_bzGmm1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlO1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:908106a0-a2cc-4fdb-b078-0bf601d3aace" ref="_bzGmnlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlPFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e368185e-eb80-4609-a70d-8e3154914669" toBeRendered="false" ref="_bzHMMFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlPVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:99e86c92-2bea-469f-bd3a-feddb088729b" ref="_bzHMM1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlPlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:d40a9308-6f12-4ab2-b022-d11baa8dbab9" ref="_bzHMNlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlP1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b91b22c5-355f-473d-a699-c04267bc261d" ref="_bzHMOVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlQFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:2d5f51a5-464b-41ca-a45c-4a0ecc35d5d4" toBeRendered="false" ref="_bzHMPFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlQVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:26699de7-39fe-4e5f-8f47-5d1f3b5424d8" ref="_bzHMP1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlQlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:55625e4c-cfcf-4ec1-a7a9-4dcd7aced62b" toBeRendered="false" ref="_bzHMQlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlQ1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9eda782d-f967-480e-9cf8-fc97e1f04d5c" toBeRendered="false" ref="_bzHMRVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzGlRFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:40d75479-ba62-4c5c-983e-9f55fba0c616" toBeRendered="false" ref="_bzHMSFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_bzGlRVXtEfCMzZJvQuNmkQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzGlRlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_bzGmRFXtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzGlR1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_bzGmRlXtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzGlSFXtEfCMzZJvQuNmkQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlSVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_bzGmQlXtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_bzGlSlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.product.CSStudioPerspective" selectedElement="_bzGlS1XtEfCMzZJvQuNmkQ" label="CS-Studio" iconURI="platform:/plugin/org.csstudio.utility.product/icons/css16.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.perspSC:org.csstudio.utility.product.CSStudioPerspective</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.csstudio.utility.clock.ClockView</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlS1XtEfCMzZJvQuNmkQ" selectedElement="_bzGlUFXtEfCMzZJvQuNmkQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_bzGlTFXtEfCMzZJvQuNmkQ" elementId="left" containerData="2500" selectedElement="_bzGlTVXtEfCMzZJvQuNmkQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzGlTVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" ref="_bzGmV1XtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzGlTlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe" toBeRendered="false" ref="_bzGmelXtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:CSS</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzGlT1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe:*" toBeRendered="false" ref="_bzGme1XtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:CSS</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlUFXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlUVXtEfCMzZJvQuNmkQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlUVXtEfCMzZJvQuNmkQ" containerData="6600" selectedElement="_bzGlU1XtEfCMzZJvQuNmkQ">
                <children xsi:type="basic:PartStack" xmi:id="_bzGlUlXtEfCMzZJvQuNmkQ" elementId="top" toBeRendered="false" containerData="6600"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlU1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" containerData="3400" ref="_bzGmNVXtEfCMzZJvQuNmkQ"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzGlVFXtEfCMzZJvQuNmkQ" elementId="bottom" toBeRendered="false" containerData="3400">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlVVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_bzGmeVXtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_bzGlVlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opieditor" selectedElement="_bzGlV1XtEfCMzZJvQuNmkQ" label="OPI Editor" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.help.ui.HelpView</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newOPIWizard</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newJSWizard</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newPyWizard</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlV1XtEfCMzZJvQuNmkQ" selectedElement="_bzGlXlXtEfCMzZJvQuNmkQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlWFXtEfCMzZJvQuNmkQ" containerData="2000" selectedElement="_bzGlWVXtEfCMzZJvQuNmkQ">
              <children xsi:type="basic:PartStack" xmi:id="_bzGlWVXtEfCMzZJvQuNmkQ" elementId="left" containerData="7000" selectedElement="_bzGlWlXtEfCMzZJvQuNmkQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlWlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" ref="_bzGmV1XtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzGlW1XtEfCMzZJvQuNmkQ" elementId="leftBottom" containerData="3000" selectedElement="_bzGlXFXtEfCMzZJvQuNmkQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlXFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_bzGmhVXtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlXVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_bzGmllXtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlXlXtEfCMzZJvQuNmkQ" containerData="8000" selectedElement="_bzGlX1XtEfCMzZJvQuNmkQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bzGlX1XtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzGlYFXtEfCMzZJvQuNmkQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlYFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_bzGmNVXtEfCMzZJvQuNmkQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_bzGlYVXtEfCMzZJvQuNmkQ" elementId="bottom" containerData="2500" selectedElement="_bzGlYlXtEfCMzZJvQuNmkQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzGlYlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_bzGmRlXtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzGlY1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_bzGmeVXtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzGlZFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProblemView" ref="_bzGml1XtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzGlZVXtEfCMzZJvQuNmkQ" elementId="right" containerData="2500" selectedElement="_bzGlZlXtEfCMzZJvQuNmkQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzGlZlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_bzGmi1XtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_bzGlZ1XtEfCMzZJvQuNmkQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_bzGlaFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_bzGmMlXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzGlaVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_bzGmM1XtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzGlalXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_bzGmNFXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmMlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmM1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmNFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_bzGmNVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" selectedElement="_bzGmNlXtEfCMzZJvQuNmkQ">
      <children xsi:type="basic:PartStack" xmi:id="_bzGmNlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_bzGmOlXtEfCMzZJvQuNmkQ">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_bzGmN1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;test.opi&quot; partName=&quot;test.opi&quot; title=&quot;test.opi&quot; tooltip=&quot;cs-studio-xf/Mobile/Test/test.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmOFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="WriteFiles.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;WriteFiles.opi&quot; partName=&quot;WriteFiles.opi&quot; title=&quot;WriteFiles.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmOVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="TimePix3Alarm.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Alarm.opi&quot; partName=&quot;TimePix3Alarm.opi&quot; title=&quot;TimePix3Alarm.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmOlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MaskStatus.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;MaskStatus.opi&quot; partName=&quot;MaskStatus.opi&quot; title=&quot;MaskStatus.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmPFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Mask.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Mask.opi&quot; partName=&quot;Mask.opi&quot; title=&quot;Mask.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmPVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="TimePix3Detector.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Detector.opi&quot; partName=&quot;TimePix3Detector.opi&quot; title=&quot;TimePix3Detector.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmPlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main.opi&quot; partName=&quot;main.opi&quot; title=&quot;main.opi&quot; tooltip=&quot;cs-studio-xf/10id/main.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/10id/main.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmP1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="pmacStatus.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;pmacStatus.opi&quot; partName=&quot;pmacStatus.opi&quot; title=&quot;pmacStatus.opi&quot; tooltip=&quot;cs-studio-xf/common/motor/pmacStatus.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/common/motor/pmacStatus.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_bzGmQFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ServalCtrl.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServalCtrl.opi&quot; partName=&quot;ServalCtrl.opi&quot; title=&quot;ServalCtrl.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmQVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmQlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmQ1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmRFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmRVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmRlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bzGmR1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmSlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmV1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view LINK_NAVIGATOR_TO_EDITOR=&quot;0&quot; sorter=&quot;1&quot;>&#xA;&lt;filters>&#xA;&lt;filter element=&quot;.*&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/filters>&#xA;&lt;expanded>&#xA;&lt;element path=&quot;/cs-studio-xf&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0&quot;/>&#xA;&lt;/expanded>&#xA;&lt;selection>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/selection>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bzGmWFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmZlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmbVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmblXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmb1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmcFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/common/color_camera_pva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmcVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmclXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmc1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmdFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmdVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmdlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmd1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmeFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmeVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmelXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGme1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmfFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:4f79fe78-23be-409b-a914-8e9e2f78279f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmfVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmflXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmf1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:cf37b73e-6960-4c76-bc87-7e7b27f51f48" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="commonPlugins" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/commonPlugins.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmgFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmgVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmglXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:f6baf561-82d2-46bd-abc6-fb8c3e7fdbaa" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDROI" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=ROI1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDROI.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmg1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmhFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmhVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bzGmhlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmiFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmi1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bzGmjFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmkVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmllXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGml1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmmFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:7f17f5b6-d606-41a8-a8e5-c402cb0cd5c0" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="FileWriter" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmmVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmmlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmm1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:0573e57f-864d-404c-9fca-423e5562252f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Load BPC" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Setup/FileBPCdacs.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmnFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmnVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzGmnlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:908106a0-a2cc-4fdb-b078-0bf601d3aace" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzGmn1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzGmoFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMMFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e368185e-eb80-4609-a70d-8e3154914669" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDStdArrays" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=image1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDStdArrays.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMMVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMMlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMM1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:99e86c92-2bea-469f-bd3a-feddb088729b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDPva" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=Pva1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;,&amp;quot;EXT=null&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDPva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMNFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMNVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMNlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:d40a9308-6f12-4ab2-b022-d11baa8dbab9" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMN1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMOFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMOVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b91b22c5-355f-473d-a699-c04267bc261d" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMOlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMO1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMPFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:2d5f51a5-464b-41ca-a45c-4a0ecc35d5d4" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="asynRecord" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;pathAsyn=epics/asyn/R4-37&amp;quot;,&amp;quot;pathAutosave=epics/autosave/R5-10-3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=cam1:AsynIO&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/epics/asyn/R4-37/asynRecord.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMPVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMPlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMP1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:26699de7-39fe-4e5f-8f47-5d1f3b5424d8" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="userStringSeq_full" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/epics/GUI/NSLS2/GUI/gitlab/cs-studio-xf/adl/userStringSeq_full.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMQFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMQVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMQlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:55625e4c-cfcf-4ec1-a7a9-4dcd7aced62b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMQ1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMRFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMRVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9eda782d-f967-480e-9cf8-fc97e1f04d5c" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorVoltages" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMRlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMR1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHMSFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:40d75479-ba62-4c5c-983e-9f55fba0c616" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorVoltages" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHMSVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHMSlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_bzHMS1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMTFXtEfCMzZJvQuNmkQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMUFXtEfCMzZJvQuNmkQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMUVXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHMUlXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMaVXtEfCMzZJvQuNmkQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMbFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMb1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMclXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMdVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.OPIEditor">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHMkFXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHMkVXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMklXtEfCMzZJvQuNmkQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMllXtEfCMzZJvQuNmkQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHMl1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMmFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMmVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMmlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHMnlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_bzHMn1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss(org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHMoFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_bzHMoVXtEfCMzZJvQuNmkQ" selectedElement="_bzHMolXtEfCMzZJvQuNmkQ" x="1908" y="115" width="1916" height="1179">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMolXtEfCMzZJvQuNmkQ" selectedElement="_bzHMo1XtEfCMzZJvQuNmkQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_bzHMo1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_bzHMpFXtEfCMzZJvQuNmkQ">
        <children xsi:type="advanced:Perspective" xmi:id="_bzHMpFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective" selectedElement="_bzHMpVXtEfCMzZJvQuNmkQ" label="OPI Runtime" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMpVXtEfCMzZJvQuNmkQ" selectedElement="_bzHMqVXtEfCMzZJvQuNmkQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_bzHMplXtEfCMzZJvQuNmkQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzHMp1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_bzHNllXtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzHMqFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_bzHNnFXtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMqVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHMqlXtEfCMzZJvQuNmkQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMqlXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHMrVXtEfCMzZJvQuNmkQ">
                <children xsi:type="basic:PartStack" xmi:id="_bzHMq1XtEfCMzZJvQuNmkQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzHMrFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_bzHNmFXtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMrVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHMrlXtEfCMzZJvQuNmkQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_bzHMrlXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHMsFXtEfCMzZJvQuNmkQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHMr1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000" ref="_bzHNlFXtEfCMzZJvQuNmkQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_bzHMsFXtEfCMzZJvQuNmkQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_bzHMuVXtEfCMzZJvQuNmkQ">
                      <tags>active</tags>
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMsVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:3383f360-e563-4659-afac-2d20d6c0206e" ref="_bzHNnVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMslXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_bzHNmlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMs1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:6ca952b9-253c-4185-b118-d048300eb968" ref="_bzHNoFXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMtFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:4cf50675-0dfa-42e9-b9f2-69809541f1c1" ref="_bzHNqVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMtVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:fb634656-4671-4b93-9d19-e389d51ec11f" toBeRendered="false" ref="_bzHNslXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMtlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e27d7884-472e-4b79-ae9c-3dd74cde8257" ref="_bzHNtVXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMt1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9fc9a6db-38ca-4f22-8666-adb085ac5f06" ref="_bzHNvlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMuFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b41687fc-8a90-4ac6-8a17-f1410b5ada57" ref="_bzHNx1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMuVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:c0df44e7-417a-4e52-b15c-7404f5a949bd" ref="_bzHN0FXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMulXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:edfb2c52-a3a9-4033-bd79-2d5c4650ba76" toBeRendered="false" ref="_bzHN2VXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMu1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:c44965c7-fe05-4dff-8d79-f40a63d485bb" toBeRendered="false" ref="_bzHN3FXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMvFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:0eb86de6-c24a-4a3f-96be-7f9ed70d517b" toBeRendered="false" ref="_bzHN31XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMvVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:3c84ad01-b051-4df9-8fea-7ecdf2ed714d" ref="_bzHN4lXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMvlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b0b8f1e0-689c-403a-a505-e21c4cd97a07" ref="_bzHN7lXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHMv1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:d2006db2-50c2-4e51-b695-0fb8d9542ec0" toBeRendered="false" ref="_bzHN91XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_bzHMwFXtEfCMzZJvQuNmkQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHMwVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_bzHNmVXtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHMwlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_bzHNm1XtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzHMw1XtEfCMzZJvQuNmkQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <tags>CSS</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzHMxFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_bzHNl1XtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzHMxVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe:1" toBeRendered="false" ref="_bzHN61XtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:CSS</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_bzHMxlXtEfCMzZJvQuNmkQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHMx1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_bzHNkVXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHMyFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_bzHNklXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHMyVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_bzHNk1XtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNkVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNklXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNk1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_bzHNlFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_bzHNlVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNllXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNl1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNmFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNmVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNmlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNm1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNnFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNnVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:3383f360-e563-4659-afac-2d20d6c0206e" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNnlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNn1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNoFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:6ca952b9-253c-4185-b118-d048300eb968" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/common/color_camera_pva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNoVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNo1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNqVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:4cf50675-0dfa-42e9-b9f2-69809541f1c1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNqlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNrFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNslXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:fb634656-4671-4b93-9d19-e389d51ec11f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNs1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNtFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNtVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e27d7884-472e-4b79-ae9c-3dd74cde8257" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="FileWriter" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNtlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNuFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNvlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9fc9a6db-38ca-4f22-8666-adb085ac5f06" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Load BPC" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Setup/FileBPCdacs.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNv1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNwVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHNx1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b41687fc-8a90-4ac6-8a17-f1410b5ada57" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHNyFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHNylXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN0FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:c0df44e7-417a-4e52-b15c-7404f5a949bd" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <tags>active</tags>
      <menus xmi:id="_bzHN0VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN01XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN2VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:edfb2c52-a3a9-4033-bd79-2d5c4650ba76" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="commonPlugins" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/commonPlugins.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN2lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN21XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN3FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:c44965c7-fe05-4dff-8d79-f40a63d485bb" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDStats" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=Stats5:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDStats.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN3VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN3lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN31XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:0eb86de6-c24a-4a3f-96be-7f9ed70d517b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN4FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN4VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN4lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:3c84ad01-b051-4df9-8fea-7ecdf2ed714d" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN41XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN5VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN61XtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe:1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3-TEST:cam1:DetectorState_RBV" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view pvFormula=&quot;TPX3-TEST:cam1:DetectorState_RBV&quot; showChangeValue=&quot;true&quot; showDetails=&quot;false&quot; showMetadata=&quot;false&quot; showValue=&quot;true&quot; showViewer=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
      <menus xmi:id="_bzHN7FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN7VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN7lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:b0b8f1e0-689c-403a-a505-e21c4cd97a07" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorVoltages" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN71XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN8VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHN91XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:d2006db2-50c2-4e51-b695-0fb8d9542ec0" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="commonPlugins" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/commonPlugins.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_bzHN-FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHN-VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_bzHN-lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_bzHN-1XtEfCMzZJvQuNmkQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHN_1XtEfCMzZJvQuNmkQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOAFXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHOAVXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOGFXtEfCMzZJvQuNmkQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOG1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOHlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOIVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHOJFXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHOJVXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHOJlXtEfCMzZJvQuNmkQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHOKlXtEfCMzZJvQuNmkQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHOK1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_bzHOLFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHOLVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHOLlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHOMlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical1" side="Left"/>
    <trimBars xmi:id="_bzHOM1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_bzHONFXtEfCMzZJvQuNmkQ" selectedElement="_bzHONVXtEfCMzZJvQuNmkQ" x="20" y="18" width="650" height="445">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bzHONVXtEfCMzZJvQuNmkQ" selectedElement="_bzHONlXtEfCMzZJvQuNmkQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_bzHONlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_bzHON1XtEfCMzZJvQuNmkQ">
        <children xsi:type="advanced:Perspective" xmi:id="_bzHON1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective" selectedElement="_bzHOOFXtEfCMzZJvQuNmkQ" label="OPI Runtime" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzHOOFXtEfCMzZJvQuNmkQ" selectedElement="_bzHOPFXtEfCMzZJvQuNmkQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_bzHOOVXtEfCMzZJvQuNmkQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzHOOlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_bzHPG1XtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzHOO1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_bzHPIVXtEfCMzZJvQuNmkQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzHOPFXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHOPVXtEfCMzZJvQuNmkQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bzHOPVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHOQFXtEfCMzZJvQuNmkQ">
                <children xsi:type="basic:PartStack" xmi:id="_bzHOPlXtEfCMzZJvQuNmkQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bzHOP1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_bzHPHVXtEfCMzZJvQuNmkQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_bzHOQFXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHOQVXtEfCMzZJvQuNmkQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_bzHOQVXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzHOQ1XtEfCMzZJvQuNmkQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHOQlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000" ref="_bzHPGVXtEfCMzZJvQuNmkQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_bzHOQ1XtEfCMzZJvQuNmkQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_bzHORFXtEfCMzZJvQuNmkQ">
                      <tags>active</tags>
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHORFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9fe61535-bc27-4852-81be-47b11a0a69b3" ref="_bzHPIlXtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_bzHORVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_bzHPH1XtEfCMzZJvQuNmkQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_bzHORlXtEfCMzZJvQuNmkQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHOR1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_bzHPHlXtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_bzHOSFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_bzHPIFXtEfCMzZJvQuNmkQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bzHOSVXtEfCMzZJvQuNmkQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzHOSlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_bzHPHFXtEfCMzZJvQuNmkQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_bzHOS1XtEfCMzZJvQuNmkQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHOTFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_bzHPFlXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHOTVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_bzHPF1XtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzHOTlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_bzHPGFXtEfCMzZJvQuNmkQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPFlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPF1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPGFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_bzHPGVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_bzHPGlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPG1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPHFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPHVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPHlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPH1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPIFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPIVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bzHPIlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9fe61535-bc27-4852-81be-47b11a0a69b3" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <tags>active</tags>
      <menus xmi:id="_bzHPI1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bzHPJVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView"/>
    </sharedElements>
    <trimBars xmi:id="_bzHPK1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPLFXtEfCMzZJvQuNmkQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPMFXtEfCMzZJvQuNmkQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPMVXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHPMlXtEfCMzZJvQuNmkQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPSVXtEfCMzZJvQuNmkQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPTFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPT1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPUlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bzHPVVXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bzHPVlXtEfCMzZJvQuNmkQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHPV1XtEfCMzZJvQuNmkQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHPW1XtEfCMzZJvQuNmkQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHPXFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_bzHPXVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHPXlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bzHPX1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_bzHPY1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical1" side="Left"/>
    <trimBars xmi:id="_bzHPZFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <bindingTables xmi:id="_bzHPZVXtEfCMzZJvQuNmkQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_bzHQM1XtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHPZlXtEfCMzZJvQuNmkQ" keySequence="CTRL+F10" command="_bzH1ZVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPZ1XtEfCMzZJvQuNmkQ" keySequence="CTRL+INSERT" command="_bzH1GlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPaFXtEfCMzZJvQuNmkQ" keySequence="CTRL+PAGE_UP" command="_bzH2d1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPaVXtEfCMzZJvQuNmkQ" keySequence="CTRL+PAGE_DOWN" command="_bzH1-VXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPalXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+F3" command="_bzH2alXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPa1XtEfCMzZJvQuNmkQ" keySequence="CTRL+1" command="_bzH171XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPbFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+L" command="_bzH2nlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPbVXtEfCMzZJvQuNmkQ" keySequence="CTRL+SPACE" command="_bzH2LFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPblXtEfCMzZJvQuNmkQ" keySequence="SHIFT+DEL" command="_bzH2U1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPb1XtEfCMzZJvQuNmkQ" keySequence="CTRL+V" command="_bzH1QVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPcFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+SPACE" command="_bzH2GlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPcVXtEfCMzZJvQuNmkQ" keySequence="CTRL+A" command="_bzH2xVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPclXtEfCMzZJvQuNmkQ" keySequence="CTRL+C" command="_bzH1GlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPc1XtEfCMzZJvQuNmkQ" keySequence="CTRL+X" command="_bzH2U1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPdFXtEfCMzZJvQuNmkQ" keySequence="CTRL+Z" command="_bzH2SVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPdVXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+Z" command="_bzH28FXtEfCMzZJvQuNmkQ">
      <tags>platform:gtk</tags>
    </bindings>
    <bindings xmi:id="_bzHPdlXtEfCMzZJvQuNmkQ" keySequence="ALT+PAGE_UP" command="_bzH3AlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPd1XtEfCMzZJvQuNmkQ" keySequence="ALT+PAGE_DOWN" command="_bzH1uFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPeFXtEfCMzZJvQuNmkQ" keySequence="SHIFT+INSERT" command="_bzH1QVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPeVXtEfCMzZJvQuNmkQ" keySequence="ALT+F11" command="_bzH1jFXtEfCMzZJvQuNmkQ">
      <tags>platform:gtk</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_bzHPelXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_bzHQNFXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHPe1XtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+F7" command="_bzH2wlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPfFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+F8" command="_bzH2H1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPfVXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_bzH2cFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPflXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_bzH1o1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPf1XtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+F4" command="_bzH2UVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPgFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+F6" command="_bzH1hVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPgVXtEfCMzZJvQuNmkQ" keySequence="CTRL+F7" command="_bzH1G1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPglXtEfCMzZJvQuNmkQ" keySequence="CTRL+F8" command="_bzH18lXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPg1XtEfCMzZJvQuNmkQ" keySequence="CTRL+F11" command="_bzH2hlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPhFXtEfCMzZJvQuNmkQ" keySequence="CTRL+F4" command="_bzH2-FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPhVXtEfCMzZJvQuNmkQ" keySequence="CTRL+F6" command="_bzH1kFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPhlXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+F7" command="_bzH1yVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPh1XtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+SHIFT+L" command="_bzH1T1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPiFXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q O" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPiVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_bzHPilXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+B" command="_bzH1rVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPi1XtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+R" command="_bzH3FVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPjFXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q Q" command="_bzH1pVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPjVXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+S" command="_bzH1f1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPjlXtEfCMzZJvQuNmkQ" keySequence="CTRL+3" command="_bzH1-lXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPj1XtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q S" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPkFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_bzHPkVXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q V" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPklXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_bzHPk1XtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+G" command="_bzH1mFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPlFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+W" command="_bzH2UVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPlVXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q H" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPllXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_bzHPl1XtEfCMzZJvQuNmkQ" keySequence="CTRL+," command="_bzH1RVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPmFXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q L" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPmVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_bzHPmlXtEfCMzZJvQuNmkQ" keySequence="CTRL+-" command="_bzH2pFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPm1XtEfCMzZJvQuNmkQ" keySequence="CTRL+." command="_bzH23VXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPnFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+B" command="_bzH1nFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPnVXtEfCMzZJvQuNmkQ" keySequence="CTRL+#" command="_bzH1ZlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPnlXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+E" command="_bzH1tlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPn1XtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q X" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPoFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_bzHPoVXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q Y" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPolXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_bzHPo1XtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q Z" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPpFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_bzHPpVXtEfCMzZJvQuNmkQ" keySequence="CTRL+P" command="_bzH2YlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPplXtEfCMzZJvQuNmkQ" keySequence="CTRL+Q" command="_bzH2cFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPp1XtEfCMzZJvQuNmkQ" keySequence="CTRL+S" command="_bzH2o1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPqFXtEfCMzZJvQuNmkQ" keySequence="CTRL+W" command="_bzH2-FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPqVXtEfCMzZJvQuNmkQ" keySequence="CTRL+H" command="_bzH2K1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPqlXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_bzH1flXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPq1XtEfCMzZJvQuNmkQ" keySequence="CTRL+M" command="_bzH2J1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPrFXtEfCMzZJvQuNmkQ" keySequence="CTRL+N" command="_bzH29FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPrVXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_bzH2I1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPrlXtEfCMzZJvQuNmkQ" keySequence="CTRL+B" command="_bzH1SlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPr1XtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q B" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPsFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_bzHPsVXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Q C" command="_bzH1pVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPslXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_bzHPs1XtEfCMzZJvQuNmkQ" keySequence="CTRL+E" command="_bzH2QFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPtFXtEfCMzZJvQuNmkQ" keySequence="CTRL+F" command="_bzH1hlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPtVXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+W" command="_bzH22lXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPtlXtEfCMzZJvQuNmkQ" keySequence="CTRL+=" command="_bzH2b1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPt1XtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+N" command="_bzH2TVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPuFXtEfCMzZJvQuNmkQ" keySequence="DEL" command="_bzH1llXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPuVXtEfCMzZJvQuNmkQ" keySequence="CTRL+_" command="_bzH2IVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPulXtEfCMzZJvQuNmkQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_bzHPu1XtEfCMzZJvQuNmkQ" keySequence="CTRL+{" command="_bzH2IVXtEfCMzZJvQuNmkQ">
      <parameters xmi:id="_bzHPvFXtEfCMzZJvQuNmkQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_bzHPvVXtEfCMzZJvQuNmkQ" keySequence="ALT+-" command="_bzH1LlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPvlXtEfCMzZJvQuNmkQ" keySequence="ALT+ARROW_LEFT" command="_bzH1a1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPv1XtEfCMzZJvQuNmkQ" keySequence="ALT+ARROW_RIGHT" command="_bzH2aVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPwFXtEfCMzZJvQuNmkQ" keySequence="SHIFT+F5" command="_bzH21FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPwVXtEfCMzZJvQuNmkQ" keySequence="ALT+F7" command="_bzH1QlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPwlXtEfCMzZJvQuNmkQ" keySequence="ALT+CR" command="_bzH2DlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPw1XtEfCMzZJvQuNmkQ" keySequence="F11" command="_bzH2v1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPxFXtEfCMzZJvQuNmkQ" keySequence="F12" command="_bzH2LlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPxVXtEfCMzZJvQuNmkQ" keySequence="F2" command="_bzH1SFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPxlXtEfCMzZJvQuNmkQ" keySequence="F5" command="_bzH2hFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPx1XtEfCMzZJvQuNmkQ" keySequence="CTRL+R" command="_bzH2_FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPyFXtEfCMzZJvQuNmkQ" keySequence="CTRL+G" command="_bzH1P1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPyVXtEfCMzZJvQuNmkQ" keySequence="F8" command="_bzH2CFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPylXtEfCMzZJvQuNmkQ" keySequence="F11" command="_bzH2MlXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHPy1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_bzHQN1XtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHPzFXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+ARROW_UP" command="_bzH221XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPzVXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_bzH20FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPzlXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+INSERT" command="_bzH1u1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHPz1XtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+CR" command="_bzH2aFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP0FXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_bzH2rlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP0VXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_bzH1xVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP0lXtEfCMzZJvQuNmkQ" keySequence="CTRL+F10" command="_bzH2ZFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP01XtEfCMzZJvQuNmkQ" keySequence="CTRL+END" command="_bzH1yFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP1FXtEfCMzZJvQuNmkQ" keySequence="CTRL+BS" command="_bzH1EVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP1VXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_UP" command="_bzH1nlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP1lXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_DOWN" command="_bzH3EVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP11XtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_LEFT" command="_bzH1ElXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP2FXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_RIGHT" command="_bzH12VXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP2VXtEfCMzZJvQuNmkQ" keySequence="CTRL+HOME" command="_bzH1PlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP2lXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+Q" command="_bzH13FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP21XtEfCMzZJvQuNmkQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_bzH121XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP3FXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+J" command="_bzH10FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP3VXtEfCMzZJvQuNmkQ" keySequence="CTRL+NUMPAD_ADD" command="_bzH2ylXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP3lXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+K" command="_bzH1m1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP31XtEfCMzZJvQuNmkQ" keySequence="CTRL++" command="_bzH1h1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP4FXtEfCMzZJvQuNmkQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_bzH2Z1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP4VXtEfCMzZJvQuNmkQ" keySequence="CTRL+-" command="_bzH2olXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP4lXtEfCMzZJvQuNmkQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_bzH1oVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP41XtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+J" command="_bzH16lXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP5FXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+A" command="_bzH1N1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP5VXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_bzH151XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP5lXtEfCMzZJvQuNmkQ" keySequence="CTRL+J" command="_bzH1b1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP51XtEfCMzZJvQuNmkQ" keySequence="CTRL+K" command="_bzH1r1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP6FXtEfCMzZJvQuNmkQ" keySequence="CTRL+L" command="_bzH2QVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP6VXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_bzH1X1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP6lXtEfCMzZJvQuNmkQ" keySequence="CTRL+D" command="_bzH1e1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP61XtEfCMzZJvQuNmkQ" keySequence="CTRL+=" command="_bzH1h1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP7FXtEfCMzZJvQuNmkQ" keySequence="ALT+SHIFT+Y" command="_bzH1CFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP7VXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+DEL" command="_bzH2MFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP7lXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+X" command="_bzH1JFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP71XtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+Y" command="_bzH2oFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP8FXtEfCMzZJvQuNmkQ" keySequence="CTRL+DEL" command="_bzH2Q1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP8VXtEfCMzZJvQuNmkQ" keySequence="ALT+/" command="_bzH2sFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP8lXtEfCMzZJvQuNmkQ" keySequence="ALT+ARROW_UP" command="_bzH2_1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP81XtEfCMzZJvQuNmkQ" keySequence="ALT+ARROW_DOWN" command="_bzH1w1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP9FXtEfCMzZJvQuNmkQ" keySequence="SHIFT+END" command="_bzH2qlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP9VXtEfCMzZJvQuNmkQ" keySequence="SHIFT+CR" command="_bzH2q1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP9lXtEfCMzZJvQuNmkQ" keySequence="SHIFT+HOME" command="_bzH2i1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP91XtEfCMzZJvQuNmkQ" keySequence="END" command="_bzH2g1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP-FXtEfCMzZJvQuNmkQ" keySequence="INSERT" command="_bzH1XlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP-VXtEfCMzZJvQuNmkQ" keySequence="F2" command="_bzH1-1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP-lXtEfCMzZJvQuNmkQ" keySequence="HOME" command="_bzH2rFXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHP-1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_bzHQPlXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHP_FXtEfCMzZJvQuNmkQ" keySequence="CTRL+F2" command="_bzH2M1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP_VXtEfCMzZJvQuNmkQ" keySequence="CTRL+R" command="_bzH1H1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP_lXtEfCMzZJvQuNmkQ" keySequence="F7" command="_bzH25VXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHP_1XtEfCMzZJvQuNmkQ" keySequence="F8" command="_bzH1WVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQAFXtEfCMzZJvQuNmkQ" keySequence="F5" command="_bzH1WlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQAVXtEfCMzZJvQuNmkQ" keySequence="F6" command="_bzH2slXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQAlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiEditor" bindingContext="_bzHQPVXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQA1XtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_UP" command="_bzH2BFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQBFXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_DOWN" command="_bzH2s1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQBVXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_LEFT" command="_bzH1_FXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQBlXtEfCMzZJvQuNmkQ" keySequence="CTRL+ARROW_RIGHT" command="_bzH1YFXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQB1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_bzHQOFXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQCFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+G" command="_bzH2glXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQCVXtEfCMzZJvQuNmkQ" keySequence="F3" command="_bzH2cVXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQClXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_bzHQPFXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQC1XtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+M" command="_bzH2w1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQDFXtEfCMzZJvQuNmkQ" keySequence="ALT+CTRL+N" command="_bzH2zFXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQDVXtEfCMzZJvQuNmkQ" keySequence="CTRL+T" command="_bzH2AVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQDlXtEfCMzZJvQuNmkQ" keySequence="CTRL+W" command="_bzH1Y1XtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQD1XtEfCMzZJvQuNmkQ" keySequence="CTRL+N" command="_bzH1jVXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQEFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_bzHQQVXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQEVXtEfCMzZJvQuNmkQ" keySequence="CTRL+V" command="_bzH1IlXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQElXtEfCMzZJvQuNmkQ" keySequence="CTRL+C" command="_bzH2CVXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQE1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_bzHQP1XtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQFFXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+," command="_bzH2clXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQFVXtEfCMzZJvQuNmkQ" keySequence="CTRL+SHIFT+." command="_bzH2JVXtEfCMzZJvQuNmkQ"/>
    <bindings xmi:id="_bzHQFlXtEfCMzZJvQuNmkQ" keySequence="CTRL+G" command="_bzH2JlXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQF1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_bzHQQFXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQGFXtEfCMzZJvQuNmkQ" keySequence="CTRL+C" command="_bzH1k1XtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQGVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.console" bindingContext="_bzHQOVXtEfCMzZJvQuNmkQ">
    <bindings xmi:id="_bzHQGlXtEfCMzZJvQuNmkQ" keySequence="CTRL+D" command="_bzH22FXtEfCMzZJvQuNmkQ"/>
  </bindingTables>
  <bindingTables xmi:id="_bzHQG1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQRVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQHFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQRlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQHVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQR1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQHlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQSFXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQH1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQSVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQIFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQSlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQIVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQS1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQIlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQTFXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQI1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQTVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQJFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQTlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQJVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQT1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQJlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQUFXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQJ1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQUVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQKFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQUlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQKVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQU1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQKlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQVFXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQK1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQVVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQLFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQVlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQLVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQV1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQLlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQWFXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQL1XtEfCMzZJvQuNmkQ" bindingContext="_bzHQWVXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQMFXtEfCMzZJvQuNmkQ" bindingContext="_bzHQWlXtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQMVXtEfCMzZJvQuNmkQ" bindingContext="_bzHQW1XtEfCMzZJvQuNmkQ"/>
  <bindingTables xmi:id="_bzHQMlXtEfCMzZJvQuNmkQ" bindingContext="_bzHQXFXtEfCMzZJvQuNmkQ"/>
  <rootContext xmi:id="_bzHQM1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_bzHQNFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_bzHQNVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_bzHQNlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_bzHQN1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_bzHQOFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
      </children>
      <children xmi:id="_bzHQOVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_bzHQOlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_bzHQO1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_bzHQPFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_bzHQPVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiEditor" name="OPI Editor Context"/>
      <children xmi:id="_bzHQPlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_bzHQP1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
      </children>
      <children xmi:id="_bzHQQFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_bzHQQVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_bzHQQlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_bzHQQ1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_bzHQRFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_bzHQRVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actionSet" name="Auto::org.csstudio.opibuilder.actionSet"/>
  <rootContext xmi:id="_bzHQRlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.editor.actionSet" name="Auto::org.csstudio.opibuilder.editor.actionSet"/>
  <rootContext xmi:id="_bzHQR1XtEfCMzZJvQuNmkQ" elementId="ScreenshotActionSet" name="Auto::ScreenshotActionSet"/>
  <rootContext xmi:id="_bzHQSFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_bzHQSVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_bzHQSlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_bzHQS1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_bzHQTFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_bzHQTVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_bzHQTlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_bzHQT1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_bzHQUFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_bzHQUVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_bzHQUlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_bzHQU1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_bzHQVFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_bzHQVVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_bzHQVlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_bzHQV1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_bzHQWFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_bzHQWVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_bzHQWlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_bzHQW1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_bzHQXFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_bzHQXVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQXlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.alarm.beast.annunciator.view" label="Annunciator" iconURI="platform:/plugin/org.csstudio.alarm.beast.annunciator/icons/annunciator.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.annunciator.ui.AnnunciatorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.annunciator"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQX1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.alarm.beast.msghist.MessageHistoryView" label="Message History" iconURI="platform:/plugin/org.csstudio.alarm.beast.msghist/icons/msg_hist.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.msghist.MessageHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.msghist"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQYFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.alarm.beast.ui.alarmtable.view" label="Alarm Table" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.alarmtable/icons/alarmtable.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.alarmtable.AlarmTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.alarmtable"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQYVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.alarm.beast.ui.alarmtree.View" label="Alarm Tree" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.alarmtree/icons/alarmtree.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.alarmtree.AlarmTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.alarmtree"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQYlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.alarm.beast.ui.areapanel" label="Alarm Area Panel" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.areapanel/icons/areapanel.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.areapanel.View"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.areapanel"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQY1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.ChannelViewer" label="Channel Viewer" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/channel-viewer-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelViewer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQZFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.ChannelTreeByPropertyView" label="Channel Tree by Property" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/tree-property-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelTreeByPropertyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQZVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.PVTableByPropertyView" label="PV Table by Property" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/table-property-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.PVTableByPropertyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQZlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.WaterfallView" label="Waterfall" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/waterfall-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.WaterfallView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQZ1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.ChannelLinePlotView" label="Channel Line Plot" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/line2d-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelLinePlotView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQaFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.ChannelOrchestratorView" label="Channel Orchestrator" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/channel-orchestrator-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelOrchestratorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQaVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.debugging.jmsmonitor.view" label="JMS Monitor" iconURI="platform:/plugin/org.csstudio.debugging.jmsmonitor/icons/jmsmonitor.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.debugging.jmsmonitor.JMSMonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.debugging.jmsmonitor"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQalXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.epics.pvtree.PVTreeView" label="EPICS PV Tree" iconURI="platform:/plugin/org.csstudio.diag.epics.pvmanager.pvtree/icons/pvtree.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.epics.pvtree.PVTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.epics.pvmanager.pvtree"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQa1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.postanalyser.view" label="Post Analyzer" iconURI="platform:/plugin/org.csstudio.diag.postanalyser/icons/mainLabel.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.postanalyser.View"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.postanalyser"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQbFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQbVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.HistogramGraph2DView" label="Histogram (alpha)" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-histogram-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.HistogramGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQblXtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.LineGraph2DView" label="Line Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-line-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.LineGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQb1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.ScatterGraph2DView" label="Scatter Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-scatter-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.ScatterGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQcFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.BubbleGraph2DView" label="Bubble Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-bubble-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.BubbleGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQcVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.IntensityGraph2DView" label="Intensity Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-intensity-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.IntensityGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQclXtEfCMzZJvQuNmkQ" elementId="org.csstudio.graphene.MultiAxisLineGraph2DView" label="Multi-Axis Line Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-multiaxis-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.MultiAxisLineGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQc1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.CreateLogEntry" label="Create Log Entry" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logentry-add-16.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.CreateLogEntryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQdFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.LogTableView" label="Log Table" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logbook-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.LogTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQdVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.LogTreeView" label="Log Tree" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logbook-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.LogTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQdlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logging.ui.LoggingConfiguration" label="Logging Configuration" iconURI="platform:/plugin/org.csstudio.logging.ui/icons/utilities-log-viewer-16.png" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logging.ui.LoggingConfiguration"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logging.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQd1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logging.ui.FXLoggingConfiguration" label="Logging Configuration" iconURI="platform:/plugin/org.csstudio.logging.ui/icons/utilities-log-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logging.ui.FXLogginConfiguration"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logging.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQeFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.security.info" label="Security Info" iconURI="platform:/plugin/org.csstudio.security.ui/icons/logout.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.security.ui.internal.SecurityInfoView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.security.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQeVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.ui.CreateShift" label="Sign off" iconURI="platform:/plugin/org.csstudio.shift.ui/icons/SignOff-icon.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.shift.ui.CreateShiftView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.shift.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQelXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.ui.ShiftTableView" label="Shift Table" iconURI="platform:/plugin/org.csstudio.shift.ui/icons/shift-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.shift.ui.ShiftTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.shift.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQe1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.archiveview.ArchiveView" label="Archive Search" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/search.gif" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.search.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQfFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.sample_view" label="Inspect Samples" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/inspect.gif" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.sampleview.SampleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQfVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.exportview.ExportView" label="Export Samples" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/export.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.exportview.ExportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQflXtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.waveformview.WaveformView" label="Inspect Waveforms" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/wavesample.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.waveformview.WaveformView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQf1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.adlParser.ADLTreeView" label="ADL Tree View" iconURI="platform:/plugin/org.csstudio.utility.adlParser/icons/medm.JPG" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.adlparser.ADLTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.adlParser"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQgFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.clock.view" label="Clock" iconURI="platform:/plugin/org.csstudio.utility.clock/icons/clock.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.csstudio.utility.clock/org.csstudio.utility.clock.ClockView">
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQgVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.eliza.ElizaView" label="Therapist" iconURI="platform:/plugin/org.csstudio.utility.eliza/icons/eliza.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.eliza.ElizaView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.eliza"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQglXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.ToolboxView" label="Datasources" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/datasource-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.DataSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQg1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.ServicesView" label="Services" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/service-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.ServicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQhFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.FunctionsView" label="Formula Functions" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/formula-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.FunctionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQhVXtEfCMzZJvQuNmkQ" elementId="screenshotView" label="Screenshot" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.screenshot.view.ScreenshotView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.screenshot"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQhlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.sysmon.SysMonView" label="System Monitor" iconURI="platform:/plugin/org.csstudio.utility.sysmon/icons/sysmon.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.sysmon.SysMonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.sysmon"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQh1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQiFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQiVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQilXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQi1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQjFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQjVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQjlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQj1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQkFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQkVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQklXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQk1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQlFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQlVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search.internal.ui.SearchResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQllXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQl1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQmFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQmVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQmlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQm1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQnFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQnVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQnlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQn1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQoFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQoVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQolXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQo1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQpFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQpVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQplXtEfCMzZJvQuNmkQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQp1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQqFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQqVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQqlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQq1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQrFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQrVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.placeHolder" label="OPI Placeholder" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.PlaceHolderView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_bzHQrlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiShellSummary" label="Standalone OPI Summary" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIShellSummary"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <trimContributions xmi:id="_bzH00FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_bzH00VXtEfCMzZJvQuNmkQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_bzH00lXtEfCMzZJvQuNmkQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_bzH001XtEfCMzZJvQuNmkQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <snippets xsi:type="advanced:Perspective" xmi:id="_bzH03VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opieditor.&lt;OPI Editor>" selectedElement="_bzH03lXtEfCMzZJvQuNmkQ" label="&lt;OPI Editor>" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png">
    <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
    <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
    <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
    <tags>persp.actionSet:ScreenshotActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.help.ui.HelpView</tags>
    <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newOPIWizard</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newJSWizard</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newPyWizard</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bzH03lXtEfCMzZJvQuNmkQ" selectedElement="_bzH05VXtEfCMzZJvQuNmkQ" horizontal="true">
      <children xsi:type="basic:PartSashContainer" xmi:id="_bzH031XtEfCMzZJvQuNmkQ" containerData="2000" selectedElement="_bzH04FXtEfCMzZJvQuNmkQ">
        <children xsi:type="basic:PartStack" xmi:id="_bzH04FXtEfCMzZJvQuNmkQ" elementId="left" containerData="3591" selectedElement="_bzH04VXtEfCMzZJvQuNmkQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH04VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_bzH04lXtEfCMzZJvQuNmkQ" elementId="leftBottom" containerData="6409" selectedElement="_bzH041XtEfCMzZJvQuNmkQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH041XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ContentOutline" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH05FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_bzH05VXtEfCMzZJvQuNmkQ" containerData="8000" selectedElement="_bzH05lXtEfCMzZJvQuNmkQ" horizontal="true">
        <children xsi:type="basic:PartSashContainer" xmi:id="_bzH05lXtEfCMzZJvQuNmkQ" containerData="6876" selectedElement="_bzH051XtEfCMzZJvQuNmkQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH051XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" containerData="7500"/>
          <children xsi:type="basic:PartStack" xmi:id="_bzH06FXtEfCMzZJvQuNmkQ" elementId="bottom" containerData="2500" selectedElement="_bzH061XtEfCMzZJvQuNmkQ">
            <children xsi:type="advanced:Placeholder" xmi:id="_bzH06VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
            <children xsi:type="advanced:Placeholder" xmi:id="_bzH06lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
            <children xsi:type="advanced:Placeholder" xmi:id="_bzH061XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ProblemView" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_bzH07FXtEfCMzZJvQuNmkQ" elementId="right" containerData="3124" selectedElement="_bzH07VXtEfCMzZJvQuNmkQ">
          <tags>BOY</tags>
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH07VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.PropertySheet" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH07lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:cdb8bf80-0c1a-402c-abe3-0daab0c8ce25" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:BOY</tags>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <snippets xsi:type="advanced:Perspective" xmi:id="_bzH071XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" selectedElement="_bzH08FXtEfCMzZJvQuNmkQ" label="&lt;OPI Runtime>" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
    <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
    <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
    <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
    <tags>persp.actionSet:ScreenshotActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bzH08FXtEfCMzZJvQuNmkQ" selectedElement="_bzH09FXtEfCMzZJvQuNmkQ" horizontal="true">
      <children xsi:type="basic:PartStack" xmi:id="_bzH08VXtEfCMzZJvQuNmkQ" elementId="LEFT" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_bzH08lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:BOY</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_bzH081XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_bzH09FXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzH09VXtEfCMzZJvQuNmkQ" horizontal="true">
        <children xsi:type="basic:PartSashContainer" xmi:id="_bzH09VXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzH0-FXtEfCMzZJvQuNmkQ">
          <children xsi:type="basic:PartStack" xmi:id="_bzH09lXtEfCMzZJvQuNmkQ" elementId="TOP" toBeRendered="false" containerData="2500">
            <children xsi:type="advanced:Placeholder" xmi:id="_bzH091XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:BOY</tags>
            </children>
          </children>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bzH0-FXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzH0-VXtEfCMzZJvQuNmkQ">
            <children xsi:type="basic:PartSashContainer" xmi:id="_bzH0-VXtEfCMzZJvQuNmkQ" containerData="7500" selectedElement="_bzH0-1XtEfCMzZJvQuNmkQ" horizontal="true">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzH0-lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000"/>
              <children xsi:type="basic:PartStack" xmi:id="_bzH0-1XtEfCMzZJvQuNmkQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_bzH1AFXtEfCMzZJvQuNmkQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_bzH0_FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzH0_VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzH0_lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzH0_1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_bzH1AFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_bzH1AVXtEfCMzZJvQuNmkQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_bzH1AlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_bzH1A1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_bzH1BFXtEfCMzZJvQuNmkQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
          <children xsi:type="advanced:Placeholder" xmi:id="_bzH1BVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:BOY</tags>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <commands xmi:id="_bzH1BlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.ui.menu.app.restart" commandName="Restart CS-Studio" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1B1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1CFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1CVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_bzH3YFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1ClXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_bzH1C1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1DFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1DVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.commands.editembeddedopi" commandName="Edit Embedded OPI" description="Open embedded OPI in editor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1DlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1D1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1EFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.epics.pvtree.OpenPVTree" commandName="EPICS PV Tree" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1EVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ElXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1E1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmTable" commandName="AlarmTable" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1FFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_bzH3X1XtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1FVXtEfCMzZJvQuNmkQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_bzH1FlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1F1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.security.logout" commandName="Log out" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1GFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1GVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1GlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1G1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1HFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1HVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1HlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1H1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1IFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1IVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_bzH3WFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1IlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1I1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1JFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1JVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1JlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1J1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1KFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1KVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1KlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1K1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1LFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1LVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1LlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1L1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1MFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1MVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1MlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.probe.OpenPhoebusProbe" commandName="Probe" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1M1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1NFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1NVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1NlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.viewer.OpenShiftViewer" commandName="Search Shift" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1N1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1OFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1OVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1OlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenWaterfall" commandName="Waterfall" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1O1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1PFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1PVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1PlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1P1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.runopi" commandName="Run OPI" description="Run the OPI file currently in the editor" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1QFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.opibuilder.OPIEditor" commandName="OPI editor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1QVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1QlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1Q1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.commands.editopi" commandName="Edit OPI" description="Open current OPI in editor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1RFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.addProperty" commandName="Add Property" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1RVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1RlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1R1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1SFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1SVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1SlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1S1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1TFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1TVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1TlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1T1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_bzH3XlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1UFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1UVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.exportlogs" commandName="Export Logs" description="Export the selected logs to a .csv or .txt file" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1UlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1U1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1VFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.samples.install" commandName="Install Examples" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1VVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1VlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusChannelTable" commandName="ChannelTable" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1V1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1WFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_bzH3WVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1WVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1WlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1W1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1XFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1XVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1XlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1X1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1YFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.STEP_FRONT" commandName="Step Front" description="Change the widget order a step front" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1YVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1YlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1Y1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ZFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ZVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ZlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1Z1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1aFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1aVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1alXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.pvmanager.probe.OpenProbe" commandName="Probe" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1a1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1bFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmTree" commandName="AlarmTree" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1bVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1blXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1b1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1cFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1cVXtEfCMzZJvQuNmkQ" elementId="org.cstudio.perspectives.LoadPerspectives" commandName="Load Perspectives" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1clXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1c1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1dFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1dVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1dlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.removeTag" commandName="Remove Tag" description="Remove tag from the selected channels" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1d1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1eFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1eVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1elXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1e1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1fFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_bzH3WFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1fVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.ui.OpenShiftBuilderDialog" commandName="Start Shift" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1flXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1f1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1gFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.opibuilder.OPIRuntime" commandName="OPI runtime" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1gVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1glXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1g1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1hFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1hVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1hlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1h1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1iFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1iVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ilXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_bzH3YFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1i1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_bzH1jFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1jVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1jlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1j1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_bzH3TVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1kFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1kVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1klXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1k1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1lFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1lVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1llXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1l1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1mFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1mVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1mlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1m1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1nFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1nVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1nlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1n1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1oFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.removeProperty" commandName="Remove property" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1oVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1olXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1o1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1pFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1pVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_bzH3TlXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1plXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_bzH1p1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_bzH1qFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_bzH1qVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1qlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1q1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_bzH3WlXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1rFXtEfCMzZJvQuNmkQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_bzH1rVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1rlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1r1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1sFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1sVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1slXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_bzH3XFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1s1XtEfCMzZJvQuNmkQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_bzH1tFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.ui.menu.app.switch_workspace" commandName="Switch Workspace..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1tVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1tlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1t1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Contextual Help" description="Open the contextual help" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1uFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1uVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ulXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.modifychannel" commandName="Modify Channel" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1u1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1vFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_bzH3YVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH1vVXtEfCMzZJvQuNmkQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_bzH1vlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenChannelTreeByProperty" commandName="Channel Tree by Property" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1v1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1wFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1wVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1wlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1w1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1xFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1xVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1xlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1x1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1yFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1yVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1ylXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1y1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1zFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1zVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1zlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1z1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH10FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH10VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.diag.postanalyser.open" commandName="Post Analyzer" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH10lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH101XtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH11FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH11VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH11lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH111XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.OpenLogEntryUpdateDialog" commandName="Reply" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH12FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenChannelLinePlot" commandName="Channel Line Plot" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH12VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH12lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH121XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH13FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH13VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH13lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH131XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH14FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH14VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH14lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH141XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH15FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH15VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_bzH3YVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH15lXtEfCMzZJvQuNmkQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_bzH151XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH16FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH16VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.addTag" commandName="Add Tag" description="Tag the selected channels" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH16lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH161XtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH17FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH17VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH17lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH171XtEfCMzZJvQuNmkQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH18FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH18VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.ui.EndShiftBuilder" commandName="End Shift" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH18lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH181XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH19FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH19VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.pretune.new" commandName="New pretune" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH19lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_bzH3YFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH191XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_bzH1-FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1-VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1-lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1-1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1_FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.STEP_BACK" commandName="Step Back" description="Change the widget order a step back" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1_VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1_lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH1_1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2AFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2AVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2AlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2A1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2BFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.TO_BACK" commandName="To Back" description="Change the widget order to back" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2BVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2BlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.layoutWidgets" commandName="Layout Widgets" description="Layout Widgets" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2B1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2CFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.compactMode" commandName="Compact Mode" description="Enter/Exit compact mode" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2CVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ClXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_bzH3WVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2C1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_bzH2DFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2DVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.openProbeOPI" commandName="OPI Probe" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2DlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2D1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2EFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2EVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_bzH3X1XtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2ElXtEfCMzZJvQuNmkQ" elementId="url" name="URL"/>
    <parameters xmi:id="_bzH2E1XtEfCMzZJvQuNmkQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_bzH2FFXtEfCMzZJvQuNmkQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_bzH2FVXtEfCMzZJvQuNmkQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_bzH2FlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2F1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2GFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2GVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusEmail" commandName="EmailLabel" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2GlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2G1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2HFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2HVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.copyurltoclipboard" commandName="Copy URL to Clipboard" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2HlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.pvtable.OpenPhoebusPVTable" commandName="PV Table" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2H1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2IFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.shift.ui.CloseShiftBuilder" commandName="Close Shift" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2IVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_bzH3X1XtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2IlXtEfCMzZJvQuNmkQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_bzH2I1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2JFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2JVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2JlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2J1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2KFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2KVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2KlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2K1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2LFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2LVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2LlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2L1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2MFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2MVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2MlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.fullscreen" commandName="Full Screen" description="Enter/Exit Full Screen" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2M1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2NFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2NVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2NlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2N1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2OFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.viewer.OpenLogViewer" commandName="View Details" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2OVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2OlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2O1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2PFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Rebase Interactive" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2PVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_bzH3WFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2PlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_bzH2P1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_bzH2QFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2QVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2QlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2Q1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2RFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.OpenDataBrowserPopup" commandName="Data Browser" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2RVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2RlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.pvtree.OpenPhoebusPVTree" commandName="PV Tree" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2R1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2SFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2SVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2SlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2S1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2TFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2TVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2TlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2T1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2UFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2UVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2UlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenChannelViewer" commandName="Channel Viewer" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2U1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2VFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2VVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2VlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2V1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2WFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2WVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2WlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2W1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmPanel" commandName="Alarm Panel" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2XFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_bzH3XFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2XVXtEfCMzZJvQuNmkQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_bzH2XlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.security.login" commandName="Log in" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2X1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2YFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2YVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_bzH3WVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2YlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2Y1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.olog.properties.reviewsign" commandName="Review and Sign" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ZFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ZVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.trends.databrowser.NewDataBrowserHandler" commandName="Data Browser" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ZlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2Z1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2aFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2aVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2alXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2a1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2bFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_bzH3UVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2bVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2blXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2b1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_bzH3W1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2cFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2cVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2clXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2c1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenPVTableByProperty" commandName="PV Table by Property" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2dFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2dVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2dlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2d1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2eFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_bzH3VVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2eVXtEfCMzZJvQuNmkQ" elementId="title" name="Title"/>
    <parameters xmi:id="_bzH2elXtEfCMzZJvQuNmkQ" elementId="message" name="Message"/>
    <parameters xmi:id="_bzH2e1XtEfCMzZJvQuNmkQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_bzH2fFXtEfCMzZJvQuNmkQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_bzH2fVXtEfCMzZJvQuNmkQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_bzH2flXtEfCMzZJvQuNmkQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_bzH2f1XtEfCMzZJvQuNmkQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_bzH2gFXtEfCMzZJvQuNmkQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_bzH2gVXtEfCMzZJvQuNmkQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_bzH2glXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2g1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2hFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2hVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2hlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2h1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2iFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2iVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.opiShellsChanged" commandName="OPI Shell opened" description="OPI Shell opened" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ilXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2i1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2jFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2jVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2jlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2j1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2kFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2kVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.commands.openopiprobe" commandName="Open OPI Probe" description="Send PV name to OPI Probe" category="_bzH3TlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2klXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2k1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2lFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.pvmanager.widgets.CopyValueToClipboardHandler" commandName="Copy Value to Clipboard" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2lVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2llXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2l1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_bzH3WlXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2mFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_bzH2mVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2mlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2m1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_bzH3YFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2nFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_bzH2nVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_bzH2nlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2n1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2oFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2oVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2olXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2o1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2pFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_bzH3W1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2pVXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.adl2boy.writeColorMapToDefaultFile" commandName="Write ADL Color Map to Color Map File" description="Write the ADLs color Map to Boys Defaults" category="_bzH3VlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2plXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2p1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.utility.channel.info" commandName="Show Channel Info" description="Show details for the selected channels" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2qFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_bzH3TVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2qVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2qlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2q1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2rFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2rVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2rlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2r1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2sFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2sVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_bzH3TVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2slXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2s1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.TO_FRONT" commandName="To Front" description="Change the widget order to front" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2tFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2tVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2tlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.olog.property.shift.searchlogs" commandName="search shift logs" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2t1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2uFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2uVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_bzH3XFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH2ulXtEfCMzZJvQuNmkQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_bzH2u1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2vFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2vVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2vlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2v1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2wFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.databrowser.OpenPhoebusDatabrowserEditor" commandName="Open Phoebus Databrowser Editor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2wVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2wlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2w1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2xFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2xVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2xlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2x1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2yFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.csdata.clipboard.copy_pv" commandName="Copy PV to clipboard" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2yVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2ylXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2y1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_bzH3T1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2zFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2zVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2zlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2z1XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logging.ui.RefreshLoggers" commandName="Refresh" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH20FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH20VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenChannelOrchestrator" commandName="Channel Orchestrator" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH20lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH201XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH21FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH21VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH21lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH211XtEfCMzZJvQuNmkQ" elementId="org.csstudio.logbook.ui.OpenLogEntryBuilderDialog" commandName="Create Log Entry" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH22FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH22VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH22lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH221XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH23FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH23VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH23lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH231XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_bzH3X1XtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH24FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_bzH24VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_bzH3YVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH24lXtEfCMzZJvQuNmkQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_bzH241XtEfCMzZJvQuNmkQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_bzH25FXtEfCMzZJvQuNmkQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_bzH25VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH25lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_bzH3X1XtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH251XtEfCMzZJvQuNmkQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_bzH26FXtEfCMzZJvQuNmkQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_bzH26VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH26lXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.adl2boy.editADLwithBoy" commandName="Convert ADL File to OPI" description="Convert ADL to OPI" category="_bzH3VlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH261XtEfCMzZJvQuNmkQ" elementId="org.csstudio.ui.util.configure" commandName="Configure..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH27FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_bzH3YVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH27VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH27lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH271XtEfCMzZJvQuNmkQ" elementId="org.csstudio.channel.views.OpenWaterfallQuery" commandName="Waterfall" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH28FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH28VXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.databrowser.OpenPhoebusDatabrowser" commandName="Open Phoebus Databrowser" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH28lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH281XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH29FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_bzH3XFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH29VXtEfCMzZJvQuNmkQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_bzH29lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH291XtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2-FXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_bzH3XFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2-VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2-lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2-1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2_FXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.actions.selectParent" commandName="Select Parent" description="Select Parent Container" category="_bzH3UFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2_VXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2_lXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH2_1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3AFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_bzH3YFXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH3AVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_bzH3AlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_bzH3WlXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3A1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_bzH3WlXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH3BFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_bzH3BVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3BlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_bzH3VVXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH3B1XtEfCMzZJvQuNmkQ" elementId="title" name="Title"/>
    <parameters xmi:id="_bzH3CFXtEfCMzZJvQuNmkQ" elementId="message" name="Message"/>
    <parameters xmi:id="_bzH3CVXtEfCMzZJvQuNmkQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_bzH3ClXtEfCMzZJvQuNmkQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_bzH3C1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3DFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3DVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_bzH3VFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3DlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3D1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3EFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3EVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_bzH3U1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3ElXtEfCMzZJvQuNmkQ" elementId="org.csstudio.phoebus.integration.Launch" commandName="Launch Phoebus" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3E1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_bzH3X1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3FFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_bzH3V1XtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3FVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_bzH3WlXtEfCMzZJvQuNmkQ">
    <parameters xmi:id="_bzH3FlXtEfCMzZJvQuNmkQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_bzH3F1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_bzH3XVXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3GFXtEfCMzZJvQuNmkQ" elementId="org.csstudio.security.ui.dummycommand" commandName="org.csstudio.security.ui.dummycommand"/>
  <commands xmi:id="_bzH3GVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.csstudio.opibuilder.actionSet/org.csstudio.opibuilder.openTopFileAction" commandName="Top Files" description="Open top files in editor. e.g. Open OPIs in run mode" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3GlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::ScreenshotActionSet/OpenScreenshotWindow" commandName="Screenshot" description="Opens the screenshot window (Shortcut CTRL+ALT+S)." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3G1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3HFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3HVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3HlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3H1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3IFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3IVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3IlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3I1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3JFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3JVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3JlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3J1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3KFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3KVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3KlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3K1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3LFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3LVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3LlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3L1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3MFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3MVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3MlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3M1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3NFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3NVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3NlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3N1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3OFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3OVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3OlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3O1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3PFXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3PVXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3PlXtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <commands xmi:id="_bzH3P1XtEfCMzZJvQuNmkQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_bzH3YFXtEfCMzZJvQuNmkQ"/>
  <addons xmi:id="_bzH3QFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_bzH3QVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_bzH3QlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_bzH3Q1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_bzH3RFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_bzH3RVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_bzH3RlXtEfCMzZJvQuNmkQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_bzH3R1XtEfCMzZJvQuNmkQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_bzH3SFXtEfCMzZJvQuNmkQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon">
    <persistedState key="org.csstudio.opibuilder.opieditor.&lt;OPI Editor>" value=""/>
    <persistedState key="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" value=""/>
  </addons>
  <addons xmi:id="_bzH3SVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_bzH3SlXtEfCMzZJvQuNmkQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_bzH3S1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_bzH3TFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_bzH3TVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_bzH3TlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_bzH3T1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_bzH3UFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_bzH3UVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_bzH3UlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_bzH3U1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_bzH3VFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_bzH3VVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_bzH3VlXtEfCMzZJvQuNmkQ" elementId="org.csstudio.opibuilder.adl2boy.BOYCategory" name="BOY" description="A category to hold references to boy commands"/>
  <categories xmi:id="_bzH3V1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_bzH3WFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_bzH3WVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_bzH3WlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_bzH3W1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_bzH3XFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_bzH3XVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_bzH3XlXtEfCMzZJvQuNmkQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_bzH3X1XtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_bzH3YFXtEfCMzZJvQuNmkQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_bzH3YVXtEfCMzZJvQuNmkQ" elementId="org.eclipse.ui.category.help" name="Help"/>
</application:Application>
