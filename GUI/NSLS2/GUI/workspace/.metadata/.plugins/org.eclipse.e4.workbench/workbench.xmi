<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_red7wFLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_reejRVLBEfCFTbvfeawKYQ" bindingContexts="_refxR1LBEfCFTbvfeawKYQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServalCtrl.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;pmacStatus.opi&quot; tooltip=&quot;cs-studio-xf/common/motor/pmacStatus.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/common/motor/pmacStatus.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main.opi&quot; tooltip=&quot;cs-studio-xf/10id/main.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/10id/main.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Detector.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Status.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Status.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Status.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Alarm.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Mask.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;MaskStatus.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3API.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3API.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3API.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Dashboard.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Measurement/Dashboard.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Measurement/Dashboard.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;WriteFiles.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;test.opi&quot; tooltip=&quot;cs-studio-xf/Mobile/Test/test.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main2.opi&quot; tooltip=&quot;cs-studio-xf/10id/main2.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/10id/main2.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;DetectorConfig.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServerFileWriter.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorVoltages.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorHealth.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorHealth.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorHealth.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorConfig.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorConfig.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorConfig.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePixDetectorChip.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorChip.opi&quot;>&#xA;&lt;persistable path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorChip.opi&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_red7wVLBEfCFTbvfeawKYQ" selectedElement="_red7wlLBEfCFTbvfeawKYQ" x="5631" y="570" width="1995" height="1136">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_red7wlLBEfCFTbvfeawKYQ" selectedElement="_red7w1LBEfCFTbvfeawKYQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_red7w1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_red79lLBEfCFTbvfeawKYQ">
        <children xsi:type="advanced:Perspective" xmi:id="_red7xFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" selectedElement="_red7xVLBEfCFTbvfeawKYQ" label="&lt;OPI Runtime>" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_red7xVLBEfCFTbvfeawKYQ" selectedElement="_red7yVLBEfCFTbvfeawKYQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_red7xlLBEfCFTbvfeawKYQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_red7x1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_red84VLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_red7yFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_red891LBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_red7yVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red7ylLBEfCFTbvfeawKYQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_red7ylLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red7zVLBEfCFTbvfeawKYQ">
                <children xsi:type="basic:PartStack" xmi:id="_red7y1LBEfCFTbvfeawKYQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_red7zFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_red841LBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_red7zVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red7zlLBEfCFTbvfeawKYQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_red7zlLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red70FLBEfCFTbvfeawKYQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_red7z1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" containerData="1000" ref="_red81VLBEfCFTbvfeawKYQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_red70FLBEfCFTbvfeawKYQ" elementId="DEFAULT_VIEW" containerData="9000" selectedElement="_red70VLBEfCFTbvfeawKYQ">
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red70VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" ref="_red9DVLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red70lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_red85VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red701LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" ref="_red9EFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red71FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" ref="_red9E1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red71VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" toBeRendered="false" ref="_red9FlLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red71lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:4f79fe78-23be-409b-a914-8e9e2f78279f" toBeRendered="false" ref="_red9HFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red711LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:cf37b73e-6960-4c76-bc87-7e7b27f51f48" ref="_red9H1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red72FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:f6baf561-82d2-46bd-abc6-fb8c3e7fdbaa" ref="_red9IlLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red72VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:7f17f5b6-d606-41a8-a8e5-c402cb0cd5c0" ref="_red9OFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red72lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:0573e57f-864d-404c-9fca-423e5562252f" ref="_red9O1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red721LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:908106a0-a2cc-4fdb-b078-0bf601d3aace" ref="_reei0VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red73FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e368185e-eb80-4609-a70d-8e3154914669" toBeRendered="false" ref="_reei1FLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red73VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:99e86c92-2bea-469f-bd3a-feddb088729b" ref="_reei11LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red73lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:d40a9308-6f12-4ab2-b022-d11baa8dbab9" ref="_reei2lLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red731LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:b91b22c5-355f-473d-a699-c04267bc261d" ref="_reei3VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red74FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:2d5f51a5-464b-41ca-a45c-4a0ecc35d5d4" toBeRendered="false" ref="_reei4FLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red74VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:26699de7-39fe-4e5f-8f47-5d1f3b5424d8" ref="_reei41LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red74lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:55625e4c-cfcf-4ec1-a7a9-4dcd7aced62b" toBeRendered="false" ref="_reei5lLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red741LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9eda782d-f967-480e-9cf8-fc97e1f04d5c" toBeRendered="false" ref="_reei6VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_red75FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:40d75479-ba62-4c5c-983e-9f55fba0c616" toBeRendered="false" ref="_reei7FLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_red75VLBEfCFTbvfeawKYQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_red75lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_red85FLBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_red751LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_red85lLBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_red76FLBEfCFTbvfeawKYQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <children xsi:type="advanced:Placeholder" xmi:id="_red76VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_red84lLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_red76lLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.product.CSStudioPerspective" selectedElement="_red761LBEfCFTbvfeawKYQ" label="CS-Studio" iconURI="platform:/plugin/org.csstudio.utility.product/icons/css16.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.perspSC:org.csstudio.utility.product.CSStudioPerspective</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.csstudio.utility.clock.ClockView</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_red761LBEfCFTbvfeawKYQ" selectedElement="_red78FLBEfCFTbvfeawKYQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_red77FLBEfCFTbvfeawKYQ" elementId="left" containerData="2500" selectedElement="_red77VLBEfCFTbvfeawKYQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_red77VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" ref="_red891LBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_red77lLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe" toBeRendered="false" ref="_red9GlLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:CSS</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_red771LBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe:*" toBeRendered="false" ref="_red9G1LBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:CSS</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_red78FLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red78VLBEfCFTbvfeawKYQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_red78VLBEfCFTbvfeawKYQ" containerData="6600" selectedElement="_red781LBEfCFTbvfeawKYQ">
                <children xsi:type="basic:PartStack" xmi:id="_red78lLBEfCFTbvfeawKYQ" elementId="top" toBeRendered="false" containerData="6600"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_red781LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" containerData="3400" ref="_red81VLBEfCFTbvfeawKYQ"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_red79FLBEfCFTbvfeawKYQ" elementId="bottom" toBeRendered="false" containerData="3400">
                <children xsi:type="advanced:Placeholder" xmi:id="_red79VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_red9GVLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_red79lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opieditor" selectedElement="_red791LBEfCFTbvfeawKYQ" label="OPI Editor" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.help.ui.HelpView</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newOPIWizard</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newJSWizard</tags>
          <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newPyWizard</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_red791LBEfCFTbvfeawKYQ" selectedElement="_red7_lLBEfCFTbvfeawKYQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_red7-FLBEfCFTbvfeawKYQ" containerData="2000" selectedElement="_red7-VLBEfCFTbvfeawKYQ">
              <children xsi:type="basic:PartStack" xmi:id="_red7-VLBEfCFTbvfeawKYQ" elementId="left" containerData="7000" selectedElement="_red7-lLBEfCFTbvfeawKYQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_red7-lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" ref="_red891LBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_red7-1LBEfCFTbvfeawKYQ" elementId="leftBottom" containerData="3000" selectedElement="_red7_FLBEfCFTbvfeawKYQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_red7_FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_red9JVLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_red7_VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_red9NlLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_red7_lLBEfCFTbvfeawKYQ" containerData="8000" selectedElement="_red7_1LBEfCFTbvfeawKYQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_red7_1LBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_red8AFLBEfCFTbvfeawKYQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_red8AFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_red81VLBEfCFTbvfeawKYQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_red8AVLBEfCFTbvfeawKYQ" elementId="bottom" containerData="2500" selectedElement="_red8AlLBEfCFTbvfeawKYQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_red8AlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_red85lLBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_red8A1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_red9GVLBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_red8BFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProblemView" ref="_red9N1LBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_red8BVLBEfCFTbvfeawKYQ" elementId="right" containerData="2500" selectedElement="_red8BlLBEfCFTbvfeawKYQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_red8BlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_red9K1LBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_red8B1LBEfCFTbvfeawKYQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_red8CFLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_red80lLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_red8CVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_red801LBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_red8ClLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_red81FLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_red80lLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red801LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red81FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_red81VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" selectedElement="_red81lLBEfCFTbvfeawKYQ">
      <children xsi:type="basic:PartStack" xmi:id="_red81lLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_red831LBEfCFTbvfeawKYQ">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_red811LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;test.opi&quot; partName=&quot;test.opi&quot; title=&quot;test.opi&quot; tooltip=&quot;cs-studio-xf/Mobile/Test/test.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red82FLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="WriteFiles.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;WriteFiles.opi&quot; partName=&quot;WriteFiles.opi&quot; title=&quot;WriteFiles.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/WriteFiles.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red82VLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="TimePix3Alarm.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Alarm.opi&quot; partName=&quot;TimePix3Alarm.opi&quot; title=&quot;TimePix3Alarm.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Alarm.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red82lLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MaskStatus.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;MaskStatus.opi&quot; partName=&quot;MaskStatus.opi&quot; title=&quot;MaskStatus.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/MaskStatus.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red821LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Mask.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;Mask.opi&quot; partName=&quot;Mask.opi&quot; title=&quot;Mask.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red83FLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="TimePix3Detector.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;TimePix3Detector.opi&quot; partName=&quot;TimePix3Detector.opi&quot; title=&quot;TimePix3Detector.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red83VLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;main.opi&quot; partName=&quot;main.opi&quot; title=&quot;main.opi&quot; tooltip=&quot;cs-studio-xf/10id/main.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/10id/main.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red83lLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="pmacStatus.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;pmacStatus.opi&quot; partName=&quot;pmacStatus.opi&quot; title=&quot;pmacStatus.opi&quot; tooltip=&quot;cs-studio-xf/common/motor/pmacStatus.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/common/motor/pmacStatus.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_red831LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ServalCtrl.opi" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.csstudio.opibuilder.OPIEditor&quot; name=&quot;ServalCtrl.opi&quot; partName=&quot;ServalCtrl.opi&quot; title=&quot;ServalCtrl.opi&quot; tooltip=&quot;cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.csstudio.opibuilder.OPIEditor</tags>
          <tags>active</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red84VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red84lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red841LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red85FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red85VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red85lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_red851LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red86lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red891LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view LINK_NAVIGATOR_TO_EDITOR=&quot;0&quot; sorter=&quot;1&quot;>&#xA;&lt;filters>&#xA;&lt;filter element=&quot;.*&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/filters>&#xA;&lt;expanded>&#xA;&lt;element path=&quot;/cs-studio-xf&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3&quot;/>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0&quot;/>&#xA;&lt;/expanded>&#xA;&lt;selection>&#xA;&lt;element path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/selection>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_red8-FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9BlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9DVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9DlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9D1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9EFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/common/color_camera_pva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9EVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9ElLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9E1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9FFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9FVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9FlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9F1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9GFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9GVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9GlLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9G1LBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9HFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:4f79fe78-23be-409b-a914-8e9e2f78279f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9HVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9HlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9H1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:cf37b73e-6960-4c76-bc87-7e7b27f51f48" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="commonPlugins" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/commonPlugins.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9IFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9IVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9IlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:f6baf561-82d2-46bd-abc6-fb8c3e7fdbaa" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDROI" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=ROI1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDROI.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9I1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9JFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9JVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_red9JlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9KFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9K1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_red9LFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9MVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9NlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9N1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9OFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:7f17f5b6-d606-41a8-a8e5-c402cb0cd5c0" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="FileWriter" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9OVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_red9OlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_red9O1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:0573e57f-864d-404c-9fca-423e5562252f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Load BPC" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Setup/FileBPCdacs.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_red9PFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei0FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei0VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:908106a0-a2cc-4fdb-b078-0bf601d3aace" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei0lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei01LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei1FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e368185e-eb80-4609-a70d-8e3154914669" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDStdArrays" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=image1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDStdArrays.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei1VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei1lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei11LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:99e86c92-2bea-469f-bd3a-feddb088729b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDPva" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=Pva1:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;,&amp;quot;EXT=null&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDPva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei2FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei2VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei2lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:d40a9308-6f12-4ab2-b022-d11baa8dbab9" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei21LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei3FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei3VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:b91b22c5-355f-473d-a699-c04267bc261d" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei3lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei31LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei4FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:2d5f51a5-464b-41ca-a45c-4a0ecc35d5d4" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="asynRecord" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;pathAsyn=epics/asyn/R4-37&amp;quot;,&amp;quot;pathAutosave=epics/autosave/R5-10-3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=cam1:AsynIO&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/epics/asyn/R4-37/asynRecord.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei4VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei4lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei41LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:26699de7-39fe-4e5f-8f47-5d1f3b5424d8" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="userStringSeq_full" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/epics/GUI/NSLS2/GUI/gitlab/cs-studio-xf/adl/userStringSeq_full.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei5FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei5VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei5lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:55625e4c-cfcf-4ec1-a7a9-4dcd7aced62b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei51LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei6FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei6VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9eda782d-f967-480e-9cf8-fc97e1f04d5c" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorVoltages" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei6lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei61LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reei7FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:40d75479-ba62-4c5c-983e-9f55fba0c616" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorVoltages" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Detector/TimePixDetectorVoltages.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reei7VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reei7lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_reei71LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_reei8FLBEfCFTbvfeawKYQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reei9FLBEfCFTbvfeawKYQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reei9VLBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_reei9lLBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejDVLBEfCFTbvfeawKYQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejEFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejE1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejFlLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejGVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.OPIEditor">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_reejNFLBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_reejNVLBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_reejNlLBEfCFTbvfeawKYQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_reejOlLBEfCFTbvfeawKYQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_reejO1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_reejPFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_reejPVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_reejPlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_reejQlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_reejQ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss(org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_reejRFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_reejRVLBEfCFTbvfeawKYQ" selectedElement="_reejRlLBEfCFTbvfeawKYQ" x="4010" y="221" width="1916" height="1283">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_reejRlLBEfCFTbvfeawKYQ" selectedElement="_reejR1LBEfCFTbvfeawKYQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_reejR1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_reejSFLBEfCFTbvfeawKYQ">
        <children xsi:type="advanced:Perspective" xmi:id="_reejSFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective" selectedElement="_reejSVLBEfCFTbvfeawKYQ" label="OPI Runtime" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_reejSVLBEfCFTbvfeawKYQ" selectedElement="_reejTVLBEfCFTbvfeawKYQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_reejSlLBEfCFTbvfeawKYQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_reejS1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_reekOFLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_reejTFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_reekPlLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_reejTVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_reejTlLBEfCFTbvfeawKYQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_reejTlLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_reejUVLBEfCFTbvfeawKYQ">
                <children xsi:type="basic:PartStack" xmi:id="_reejT1LBEfCFTbvfeawKYQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_reejUFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_reekOlLBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_reejUVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_reejUlLBEfCFTbvfeawKYQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_reejUlLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_reejVFLBEfCFTbvfeawKYQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_reejU1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000" ref="_reekNlLBEfCFTbvfeawKYQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_reejVFLBEfCFTbvfeawKYQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_reejWFLBEfCFTbvfeawKYQ">
                      <tags>active</tags>
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejVVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:3383f360-e563-4659-afac-2d20d6c0206e" ref="_reekP1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejVlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_reekPFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejV1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:6ca952b9-253c-4185-b118-d048300eb968" ref="_reekQlLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejWFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:4cf50675-0dfa-42e9-b9f2-69809541f1c1" ref="_reekS1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejWVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:fb634656-4671-4b93-9d19-e389d51ec11f" toBeRendered="false" ref="_reekVFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejWlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e27d7884-472e-4b79-ae9c-3dd74cde8257" ref="_reekV1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejW1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9fc9a6db-38ca-4f22-8666-adb085ac5f06" ref="_reekYFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejXFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:b41687fc-8a90-4ac6-8a17-f1410b5ada57" ref="_reekaVLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejXVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:c0df44e7-417a-4e52-b15c-7404f5a949bd" ref="_reekbFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejXlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:edfb2c52-a3a9-4033-bd79-2d5c4650ba76" toBeRendered="false" ref="_refJ4lLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejX1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:c44965c7-fe05-4dff-8d79-f40a63d485bb" ref="_refJ5VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejYFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:0eb86de6-c24a-4a3f-96be-7f9ed70d517b" toBeRendered="false" ref="_refJ7lLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_reejYVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:3c84ad01-b051-4df9-8fea-7ecdf2ed714d" ref="_refJ8VLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_reejYlLBEfCFTbvfeawKYQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_reejY1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_reekO1LBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_reejZFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_reekPVLBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_reejZVLBEfCFTbvfeawKYQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <tags>CSS</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_reejZlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_reekOVLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_reejZ1LBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe:1" toBeRendered="false" ref="_refJ9FLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:CSS</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_reejaFLBEfCFTbvfeawKYQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_reejaVLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_reekM1LBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_reejalLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_reekNFLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_reeja1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_reekNVLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekM1LBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekNFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekNVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_reekNlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_reekN1LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekOFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekOVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekOlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekO1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekPFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekPVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekPlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekP1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:3383f360-e563-4659-afac-2d20d6c0206e" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="test.opi" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/Mobile/Test/test.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekQFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekQVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekQlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:6ca952b9-253c-4185-b118-d048300eb968" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/common/color_camera_pva.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekQ1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekRVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekS1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:4cf50675-0dfa-42e9-b9f2-69809541f1c1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <tags>active</tags>
      <menus xmi:id="_reekTFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekTlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekVFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:fb634656-4671-4b93-9d19-e389d51ec11f" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekVVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekVlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekV1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e27d7884-472e-4b79-ae9c-3dd74cde8257" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="FileWriter" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/ServerFileWriter.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekWFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekWlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekYFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9fc9a6db-38ca-4f22-8666-adb085ac5f06" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Load BPC" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Setup/FileBPCdacs.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekYVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekY1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekaVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:b41687fc-8a90-4ac6-8a17-f1410b5ada57" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Mask" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Mask/Mask.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekalLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reeka1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_reekbFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:c0df44e7-417a-4e52-b15c-7404f5a949bd" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/Acquire/DetectorConfig.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_reekbVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_reekb1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refJ4lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:edfb2c52-a3a9-4033-bd79-2d5c4650ba76" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="commonPlugins" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/commonPlugins.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_refJ41LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refJ5FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refJ5VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:c44965c7-fe05-4dff-8d79-f40a63d485bb" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NDStats" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=TPX3-TEST:&amp;quot;,&amp;quot;R=Stats5:&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADCore/R3-11/NDStats.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_refJ5lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refJ6FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refJ7lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:0eb86de6-c24a-4a3f-96be-7f9ed70d517b" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_refJ71LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refJ8FLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refJ8VLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:3c84ad01-b051-4df9-8fea-7ecdf2ed714d" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TimePix3Detector" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input macro=&quot;&amp;quot;true&amp;quot;,&amp;quot;LCID=LCID_3&amp;quot;,&amp;quot;P=$(Sys)$(Dev)&amp;quot;,&amp;quot;R=$(Cam)&amp;quot;,&amp;quot;CORE_VER=R3-11&amp;quot;,&amp;quot;XF=IXS&amp;quot;,&amp;quot;Dev=:&amp;quot;,&amp;quot;Sys=TPX3-TEST&amp;quot;,&amp;quot;IXS=XF:10ID&amp;quot;,&amp;quot;Cam=cam1:&amp;quot;,&amp;quot;Image=image1:&amp;quot;,&amp;quot;Magic=IXSDIAG2&amp;quot;,&amp;quot;NAME=TPX3&amp;quot;,&amp;quot;C=TimePix&amp;quot;,&amp;quot;Title=TimePix3&amp;quot;,&amp;quot;Stats=Stats1:&amp;quot;,&amp;quot;Trans=Trans1:&amp;quot;,&amp;quot;Proc=Proc1:&amp;quot;,&amp;quot;Pva=Pva1:&amp;quot;,&amp;quot;pathADCore=ADet/R3-11/ADCore/R3-11&amp;quot;&quot; path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/TimePix3Detector.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <menus xmi:id="_refJ8lLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refJ81LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refJ9FLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe:1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3-TEST:cam1:DetectorState_RBV" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view pvFormula=&quot;TPX3-TEST:cam1:DetectorState_RBV&quot; showChangeValue=&quot;true&quot; showDetails=&quot;false&quot; showMetadata=&quot;false&quot; showValue=&quot;true&quot; showViewer=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:CSS</tags>
      <menus xmi:id="_refJ9VLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refJ9lLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_refJ91LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_refJ-FLBEfCFTbvfeawKYQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refJ_FLBEfCFTbvfeawKYQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refJ_VLBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_refJ_lLBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refKFVLBEfCFTbvfeawKYQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refKGFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refKG1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refKHlLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refKIVLBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_refKIlLBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refKI1LBEfCFTbvfeawKYQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refKJ1LBEfCFTbvfeawKYQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_refKKFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_refKKVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refKKlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refKK1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_refKL1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical1" side="Left"/>
    <trimBars xmi:id="_refKMFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_refKMVLBEfCFTbvfeawKYQ" selectedElement="_refKMlLBEfCFTbvfeawKYQ" x="799" y="2" width="650" height="445">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_refKMlLBEfCFTbvfeawKYQ" selectedElement="_refKM1LBEfCFTbvfeawKYQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_refKM1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_refKNFLBEfCFTbvfeawKYQ">
        <children xsi:type="advanced:Perspective" xmi:id="_refKNFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective" selectedElement="_refKNVLBEfCFTbvfeawKYQ" label="OPI Runtime" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
          <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
          <tags>persp.actionSet:ScreenshotActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_refKNVLBEfCFTbvfeawKYQ" selectedElement="_refKOVLBEfCFTbvfeawKYQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_refKNlLBEfCFTbvfeawKYQ" elementId="LEFT" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_refKN1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" ref="_refLGFLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_refKOFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_refLHlLBEfCFTbvfeawKYQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_refKOVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_refKOlLBEfCFTbvfeawKYQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_refKOlLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_refKPVLBEfCFTbvfeawKYQ">
                <children xsi:type="basic:PartStack" xmi:id="_refKO1LBEfCFTbvfeawKYQ" elementId="TOP" toBeRendered="false" containerData="2500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_refKPFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" ref="_refLGlLBEfCFTbvfeawKYQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:BOY</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_refKPVLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_refKPlLBEfCFTbvfeawKYQ">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_refKPlLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_refKQFLBEfCFTbvfeawKYQ" horizontal="true">
                    <children xsi:type="advanced:Placeholder" xmi:id="_refKP1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000" ref="_refLFlLBEfCFTbvfeawKYQ"/>
                    <children xsi:type="basic:PartStack" xmi:id="_refKQFLBEfCFTbvfeawKYQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_refKQVLBEfCFTbvfeawKYQ">
                      <tags>active</tags>
                      <tags>noFocus</tags>
                      <children xsi:type="advanced:Placeholder" xmi:id="_refKQVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9fe61535-bc27-4852-81be-47b11a0a69b3" ref="_refLH1LBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_refKQlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" ref="_refLHFLBEfCFTbvfeawKYQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:BOY</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_refKQ1LBEfCFTbvfeawKYQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_refKRFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" ref="_refLG1LBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:BOY</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_refKRVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_refLHVLBEfCFTbvfeawKYQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_refKRlLBEfCFTbvfeawKYQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
                <children xsi:type="advanced:Placeholder" xmi:id="_refKR1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" ref="_refLGVLBEfCFTbvfeawKYQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_refKSFLBEfCFTbvfeawKYQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_refKSVLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_refLE1LBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_refKSlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_refLFFLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_refKS1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_refLFVLBEfCFTbvfeawKYQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLE1LBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLFFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLFVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_refLFlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_refLF1LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLGFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLGVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLGlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLG1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLHFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLHVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLHlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_refLH1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9fe61535-bc27-4852-81be-47b11a0a69b3" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TPX3DetectorConfig" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
      <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view factory_id=&quot;org.csstudio.opibuilder.runmode.RunnerInputFactory&quot;>&#xA;&lt;input path=&quot;/cs-studio-xf/ADet/R3-11/ADTimePix3/R1-0/ServalCtrl.opi&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:BOY</tags>
      <tags>active</tags>
      <menus xmi:id="_refLIFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_refLIlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView"/>
    </sharedElements>
    <trimBars xmi:id="_refLKFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_refLKVLBEfCFTbvfeawKYQ" elementId="file">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLLVLBEfCFTbvfeawKYQ" elementId="user" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLLlLBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_refLL1LBEfCFTbvfeawKYQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLRlLBEfCFTbvfeawKYQ" elementId="ScreenshotActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLSVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLTFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLT1LBEfCFTbvfeawKYQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_refLUlLBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_refLU1LBEfCFTbvfeawKYQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refLVFLBEfCFTbvfeawKYQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refLWFLBEfCFTbvfeawKYQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_refLWVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_refLWlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refLW1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_refLXFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_refLYFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical1" side="Left"/>
    <trimBars xmi:id="_refLYVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <bindingTables xmi:id="_refLYlLBEfCFTbvfeawKYQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_refxR1LBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refLY1LBEfCFTbvfeawKYQ" keySequence="CTRL+F10" command="_rega7FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLZFLBEfCFTbvfeawKYQ" keySequence="CTRL+INSERT" command="_regaoVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLZVLBEfCFTbvfeawKYQ" keySequence="CTRL+PAGE_UP" command="_regb_lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLZlLBEfCFTbvfeawKYQ" keySequence="CTRL+PAGE_DOWN" command="_regbgFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLZ1LBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+F3" command="_regb8VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLaFLBEfCFTbvfeawKYQ" keySequence="CTRL+1" command="_regbdlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLaVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+L" command="_regcJVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLalLBEfCFTbvfeawKYQ" keySequence="CTRL+SPACE" command="_regbs1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLa1LBEfCFTbvfeawKYQ" keySequence="SHIFT+DEL" command="_regb2lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLbFLBEfCFTbvfeawKYQ" keySequence="CTRL+V" command="_regayFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLbVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+SPACE" command="_regboVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLblLBEfCFTbvfeawKYQ" keySequence="CTRL+A" command="_reg_FlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLb1LBEfCFTbvfeawKYQ" keySequence="CTRL+C" command="_regaoVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLcFLBEfCFTbvfeawKYQ" keySequence="CTRL+X" command="_regb2lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLcVLBEfCFTbvfeawKYQ" keySequence="CTRL+Z" command="_regb0FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLclLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+Z" command="_reg_QVLBEfCFTbvfeawKYQ">
      <tags>platform:gtk</tags>
    </bindings>
    <bindings xmi:id="_refLc1LBEfCFTbvfeawKYQ" keySequence="ALT+PAGE_UP" command="_reg_U1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLdFLBEfCFTbvfeawKYQ" keySequence="ALT+PAGE_DOWN" command="_regbP1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLdVLBEfCFTbvfeawKYQ" keySequence="SHIFT+INSERT" command="_regayFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLdlLBEfCFTbvfeawKYQ" keySequence="ALT+F11" command="_regbE1LBEfCFTbvfeawKYQ">
      <tags>platform:gtk</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_refLd1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_refxSFLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refLeFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+F7" command="_reg_E1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLeVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+F8" command="_regbplLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLelLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_regb91LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLe1LBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_regbKlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLfFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+F4" command="_regb2FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLfVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+F6" command="_regbDFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLflLBEfCFTbvfeawKYQ" keySequence="CTRL+F7" command="_regaolLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLf1LBEfCFTbvfeawKYQ" keySequence="CTRL+F8" command="_regbeVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLgFLBEfCFTbvfeawKYQ" keySequence="CTRL+F11" command="_regcDVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLgVLBEfCFTbvfeawKYQ" keySequence="CTRL+F4" command="_reg_SVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLglLBEfCFTbvfeawKYQ" keySequence="CTRL+F6" command="_regbF1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLg1LBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+F7" command="_regbUFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLhFLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+SHIFT+L" command="_rega1lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLhVLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q O" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLhlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_refLh1LBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+B" command="_regbNFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLiFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+R" command="_reg_ZlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLiVLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q Q" command="_regbLFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLilLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+S" command="_regbBlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLi1LBEfCFTbvfeawKYQ" keySequence="CTRL+3" command="_regbgVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLjFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q S" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLjVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_refLjlLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q V" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLj1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_refLkFLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+G" command="_regbH1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLkVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+W" command="_regb2FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLklLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q H" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLk1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_refLlFLBEfCFTbvfeawKYQ" keySequence="CTRL+," command="_regazFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLlVLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q L" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLllLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_refLl1LBEfCFTbvfeawKYQ" keySequence="CTRL+-" command="_regcK1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLmFLBEfCFTbvfeawKYQ" keySequence="CTRL+." command="_reg_LlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLmVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+B" command="_regbI1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLmlLBEfCFTbvfeawKYQ" keySequence="CTRL+#" command="_rega7VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLm1LBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+E" command="_regbPVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLnFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q X" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLnVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_refLnlLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q Y" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLn1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_refLoFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q Z" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLoVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_refLolLBEfCFTbvfeawKYQ" keySequence="CTRL+P" command="_regb6VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLo1LBEfCFTbvfeawKYQ" keySequence="CTRL+Q" command="_regb91LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLpFLBEfCFTbvfeawKYQ" keySequence="CTRL+S" command="_regcKlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLpVLBEfCFTbvfeawKYQ" keySequence="CTRL+W" command="_reg_SVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLplLBEfCFTbvfeawKYQ" keySequence="CTRL+H" command="_regbslLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLp1LBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_regbBVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLqFLBEfCFTbvfeawKYQ" keySequence="CTRL+M" command="_regbrlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLqVLBEfCFTbvfeawKYQ" keySequence="CTRL+N" command="_reg_RVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLqlLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_regbqlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLq1LBEfCFTbvfeawKYQ" keySequence="CTRL+B" command="_rega0VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLrFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q B" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLrVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_refLrlLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Q C" command="_regbLFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLr1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_refLsFLBEfCFTbvfeawKYQ" keySequence="CTRL+E" command="_regbx1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLsVLBEfCFTbvfeawKYQ" keySequence="CTRL+F" command="_regbDVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLslLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+W" command="_reg_K1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLs1LBEfCFTbvfeawKYQ" keySequence="CTRL+=" command="_regb9lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLtFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+N" command="_regb1FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLtVLBEfCFTbvfeawKYQ" keySequence="DEL" command="_regbHVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLtlLBEfCFTbvfeawKYQ" keySequence="CTRL+_" command="_regbqFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLt1LBEfCFTbvfeawKYQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_refLuFLBEfCFTbvfeawKYQ" keySequence="CTRL+{" command="_regbqFLBEfCFTbvfeawKYQ">
      <parameters xmi:id="_refLuVLBEfCFTbvfeawKYQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_refLulLBEfCFTbvfeawKYQ" keySequence="ALT+-" command="_regatVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLu1LBEfCFTbvfeawKYQ" keySequence="ALT+ARROW_LEFT" command="_rega8lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLvFLBEfCFTbvfeawKYQ" keySequence="ALT+ARROW_RIGHT" command="_regb8FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLvVLBEfCFTbvfeawKYQ" keySequence="SHIFT+F5" command="_reg_JVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLvlLBEfCFTbvfeawKYQ" keySequence="ALT+F7" command="_regayVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLv1LBEfCFTbvfeawKYQ" keySequence="ALT+CR" command="_regblVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLwFLBEfCFTbvfeawKYQ" keySequence="F11" command="_reg_EFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLwVLBEfCFTbvfeawKYQ" keySequence="F12" command="_regbtVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLwlLBEfCFTbvfeawKYQ" keySequence="F2" command="_regaz1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLw1LBEfCFTbvfeawKYQ" keySequence="F5" command="_regcC1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLxFLBEfCFTbvfeawKYQ" keySequence="CTRL+R" command="_reg_TVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLxVLBEfCFTbvfeawKYQ" keySequence="CTRL+G" command="_regaxlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLxlLBEfCFTbvfeawKYQ" keySequence="F8" command="_regbj1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLx1LBEfCFTbvfeawKYQ" keySequence="F11" command="_regbuVLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refLyFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_refxS1LBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refLyVLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+ARROW_UP" command="_reg_LFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLylLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_reg_IVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLy1LBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+INSERT" command="_regbQlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLzFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+CR" command="_regb71LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLzVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_regcNVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLzlLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_regbTFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refLz1LBEfCFTbvfeawKYQ" keySequence="CTRL+F10" command="_regb61LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL0FLBEfCFTbvfeawKYQ" keySequence="CTRL+END" command="_regbT1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL0VLBEfCFTbvfeawKYQ" keySequence="CTRL+BS" command="_regamFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL0lLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_UP" command="_regbJVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL01LBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_DOWN" command="_reg_YlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL1FLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_LEFT" command="_regamVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL1VLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_RIGHT" command="_regbYFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL1lLBEfCFTbvfeawKYQ" keySequence="CTRL+HOME" command="_regaxVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL11LBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+Q" command="_regbY1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refL2FLBEfCFTbvfeawKYQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_regbYlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw8FLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+J" command="_regbV1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw8VLBEfCFTbvfeawKYQ" keySequence="CTRL+NUMPAD_ADD" command="_reg_G1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw8lLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+K" command="_regbIlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw81LBEfCFTbvfeawKYQ" keySequence="CTRL++" command="_regbDlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw9FLBEfCFTbvfeawKYQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_regb7lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw9VLBEfCFTbvfeawKYQ" keySequence="CTRL+-" command="_regcKVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw9lLBEfCFTbvfeawKYQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_regbKFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw91LBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+J" command="_regbcVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw-FLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+A" command="_regavlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw-VLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_regbblLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw-lLBEfCFTbvfeawKYQ" keySequence="CTRL+J" command="_rega9lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw-1LBEfCFTbvfeawKYQ" keySequence="CTRL+K" command="_regbNlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw_FLBEfCFTbvfeawKYQ" keySequence="CTRL+L" command="_regbyFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw_VLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_rega5lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw_lLBEfCFTbvfeawKYQ" keySequence="CTRL+D" command="_regbAlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refw_1LBEfCFTbvfeawKYQ" keySequence="CTRL+=" command="_regbDlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxAFLBEfCFTbvfeawKYQ" keySequence="ALT+SHIFT+Y" command="_regaj1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxAVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+DEL" command="_regbt1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxAlLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+X" command="_regaq1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxA1LBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+Y" command="_regcJ1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxBFLBEfCFTbvfeawKYQ" keySequence="CTRL+DEL" command="_regbylLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxBVLBEfCFTbvfeawKYQ" keySequence="ALT+/" command="_regcN1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxBlLBEfCFTbvfeawKYQ" keySequence="ALT+ARROW_UP" command="_reg_UFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxB1LBEfCFTbvfeawKYQ" keySequence="ALT+ARROW_DOWN" command="_regbSlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxCFLBEfCFTbvfeawKYQ" keySequence="SHIFT+END" command="_regcMVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxCVLBEfCFTbvfeawKYQ" keySequence="SHIFT+CR" command="_regcMlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxClLBEfCFTbvfeawKYQ" keySequence="SHIFT+HOME" command="_regcElLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxC1LBEfCFTbvfeawKYQ" keySequence="END" command="_regcClLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxDFLBEfCFTbvfeawKYQ" keySequence="INSERT" command="_rega5VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxDVLBEfCFTbvfeawKYQ" keySequence="F2" command="_regbglLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxDlLBEfCFTbvfeawKYQ" keySequence="HOME" command="_regcM1LBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxD1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_refxUlLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxEFLBEfCFTbvfeawKYQ" keySequence="CTRL+F2" command="_regbulLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxEVLBEfCFTbvfeawKYQ" keySequence="CTRL+R" command="_regaplLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxElLBEfCFTbvfeawKYQ" keySequence="F7" command="_reg_NlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxE1LBEfCFTbvfeawKYQ" keySequence="F8" command="_rega4FLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxFFLBEfCFTbvfeawKYQ" keySequence="F5" command="_rega4VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxFVLBEfCFTbvfeawKYQ" keySequence="F6" command="_regcOVLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxFlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiEditor" bindingContext="_refxUVLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxF1LBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_UP" command="_regbi1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxGFLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_DOWN" command="_regcOlLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxGVLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_LEFT" command="_regbg1LBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxGlLBEfCFTbvfeawKYQ" keySequence="CTRL+ARROW_RIGHT" command="_rega51LBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxG1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_refxTFLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxHFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+G" command="_regcCVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxHVLBEfCFTbvfeawKYQ" keySequence="F3" command="_regb-FLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxHlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_refxUFLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxH1LBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+M" command="_reg_FFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxIFLBEfCFTbvfeawKYQ" keySequence="ALT+CTRL+N" command="_reg_HVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxIVLBEfCFTbvfeawKYQ" keySequence="CTRL+T" command="_regbiFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxIlLBEfCFTbvfeawKYQ" keySequence="CTRL+W" command="_rega6lLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxI1LBEfCFTbvfeawKYQ" keySequence="CTRL+N" command="_regbFFLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxJFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_refxVVLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxJVLBEfCFTbvfeawKYQ" keySequence="CTRL+V" command="_regaqVLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxJlLBEfCFTbvfeawKYQ" keySequence="CTRL+C" command="_regbkFLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxJ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_refxU1LBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxKFLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+," command="_regb-VLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxKVLBEfCFTbvfeawKYQ" keySequence="CTRL+SHIFT+." command="_regbrFLBEfCFTbvfeawKYQ"/>
    <bindings xmi:id="_refxKlLBEfCFTbvfeawKYQ" keySequence="CTRL+G" command="_regbrVLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxK1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_refxVFLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxLFLBEfCFTbvfeawKYQ" keySequence="CTRL+C" command="_regbGlLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxLVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.console" bindingContext="_refxTVLBEfCFTbvfeawKYQ">
    <bindings xmi:id="_refxLlLBEfCFTbvfeawKYQ" keySequence="CTRL+D" command="_reg_KVLBEfCFTbvfeawKYQ"/>
  </bindingTables>
  <bindingTables xmi:id="_refxL1LBEfCFTbvfeawKYQ" bindingContext="_refxWVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxMFLBEfCFTbvfeawKYQ" bindingContext="_refxWlLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxMVLBEfCFTbvfeawKYQ" bindingContext="_refxW1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxMlLBEfCFTbvfeawKYQ" bindingContext="_refxXFLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxM1LBEfCFTbvfeawKYQ" bindingContext="_refxXVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxNFLBEfCFTbvfeawKYQ" bindingContext="_refxXlLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxNVLBEfCFTbvfeawKYQ" bindingContext="_refxX1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxNlLBEfCFTbvfeawKYQ" bindingContext="_refxYFLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxN1LBEfCFTbvfeawKYQ" bindingContext="_refxYVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxOFLBEfCFTbvfeawKYQ" bindingContext="_refxYlLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxOVLBEfCFTbvfeawKYQ" bindingContext="_refxY1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxOlLBEfCFTbvfeawKYQ" bindingContext="_refxZFLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxO1LBEfCFTbvfeawKYQ" bindingContext="_refxZVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxPFLBEfCFTbvfeawKYQ" bindingContext="_refxZlLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxPVLBEfCFTbvfeawKYQ" bindingContext="_refxZ1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxPlLBEfCFTbvfeawKYQ" bindingContext="_refxaFLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxP1LBEfCFTbvfeawKYQ" bindingContext="_refxaVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxQFLBEfCFTbvfeawKYQ" bindingContext="_refxalLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxQVLBEfCFTbvfeawKYQ" bindingContext="_refxa1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxQlLBEfCFTbvfeawKYQ" bindingContext="_refxbFLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxQ1LBEfCFTbvfeawKYQ" bindingContext="_refxbVLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxRFLBEfCFTbvfeawKYQ" bindingContext="_refxblLBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxRVLBEfCFTbvfeawKYQ" bindingContext="_refxb1LBEfCFTbvfeawKYQ"/>
  <bindingTables xmi:id="_refxRlLBEfCFTbvfeawKYQ" bindingContext="_refxcFLBEfCFTbvfeawKYQ"/>
  <rootContext xmi:id="_refxR1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_refxSFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_refxSVLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_refxSlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_refxS1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_refxTFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
      </children>
      <children xmi:id="_refxTVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_refxTlLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_refxT1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_refxUFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_refxUVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiEditor" name="OPI Editor Context"/>
      <children xmi:id="_refxUlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_refxU1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
      </children>
      <children xmi:id="_refxVFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_refxVVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_refxVlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_refxV1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_refxWFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_refxWVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actionSet" name="Auto::org.csstudio.opibuilder.actionSet"/>
  <rootContext xmi:id="_refxWlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.editor.actionSet" name="Auto::org.csstudio.opibuilder.editor.actionSet"/>
  <rootContext xmi:id="_refxW1LBEfCFTbvfeawKYQ" elementId="ScreenshotActionSet" name="Auto::ScreenshotActionSet"/>
  <rootContext xmi:id="_refxXFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_refxXVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_refxXlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_refxX1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_refxYFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_refxYVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_refxYlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_refxY1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_refxZFLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_refxZVLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_refxZlLBEfCFTbvfeawKYQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_refxZ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_refxaFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_refxaVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_refxalLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_refxa1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_refxbFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_refxbVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_refxblLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_refxb1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_refxcFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_refxcVLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_refxclLBEfCFTbvfeawKYQ" elementId="org.csstudio.alarm.beast.annunciator.view" label="Annunciator" iconURI="platform:/plugin/org.csstudio.alarm.beast.annunciator/icons/annunciator.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.annunciator.ui.AnnunciatorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.annunciator"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxc1LBEfCFTbvfeawKYQ" elementId="org.csstudio.alarm.beast.msghist.MessageHistoryView" label="Message History" iconURI="platform:/plugin/org.csstudio.alarm.beast.msghist/icons/msg_hist.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.msghist.MessageHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.msghist"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxdFLBEfCFTbvfeawKYQ" elementId="org.csstudio.alarm.beast.ui.alarmtable.view" label="Alarm Table" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.alarmtable/icons/alarmtable.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.alarmtable.AlarmTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.alarmtable"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxdVLBEfCFTbvfeawKYQ" elementId="org.csstudio.alarm.beast.ui.alarmtree.View" label="Alarm Tree" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.alarmtree/icons/alarmtree.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.alarmtree.AlarmTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.alarmtree"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxdlLBEfCFTbvfeawKYQ" elementId="org.csstudio.alarm.beast.ui.areapanel" label="Alarm Area Panel" iconURI="platform:/plugin/org.csstudio.alarm.beast.ui.areapanel/icons/areapanel.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.alarm.beast.ui.areapanel.View"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.alarm.beast.ui.areapanel"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxd1LBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.ChannelViewer" label="Channel Viewer" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/channel-viewer-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelViewer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxeFLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.ChannelTreeByPropertyView" label="Channel Tree by Property" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/tree-property-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelTreeByPropertyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxeVLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.PVTableByPropertyView" label="PV Table by Property" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/table-property-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.PVTableByPropertyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxelLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.WaterfallView" label="Waterfall" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/waterfall-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.WaterfallView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxe1LBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.ChannelLinePlotView" label="Channel Line Plot" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/line2d-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelLinePlotView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxfFLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.ChannelOrchestratorView" label="Channel Orchestrator" iconURI="platform:/plugin/org.csstudio.channel.widgets/icons/channel-orchestrator-16.png" tooltip="" category="Channel Applications" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.channel.views.ChannelOrchestratorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.channel.views"/>
    <tags>View</tags>
    <tags>categoryTag:Channel Applications</tags>
  </descriptors>
  <descriptors xmi:id="_refxfVLBEfCFTbvfeawKYQ" elementId="org.csstudio.debugging.jmsmonitor.view" label="JMS Monitor" iconURI="platform:/plugin/org.csstudio.debugging.jmsmonitor/icons/jmsmonitor.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.debugging.jmsmonitor.JMSMonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.debugging.jmsmonitor"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxflLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.epics.pvtree.PVTreeView" label="EPICS PV Tree" iconURI="platform:/plugin/org.csstudio.diag.epics.pvmanager.pvtree/icons/pvtree.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.epics.pvtree.PVTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.epics.pvmanager.pvtree"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxf1LBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.postanalyser.view" label="Post Analyzer" iconURI="platform:/plugin/org.csstudio.diag.postanalyser/icons/mainLabel.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.postanalyser.View"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.postanalyser"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxgFLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe" label="Probe" iconURI="platform:/plugin/org.csstudio.diag.pvmanager.probe/icons/probe-16.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.diag.pvmanager.probe.PVManagerProbe"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.diag.pvmanager.probe"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxgVLBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.HistogramGraph2DView" label="Histogram (alpha)" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-histogram-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.HistogramGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxglLBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.LineGraph2DView" label="Line Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-line-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.LineGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxg1LBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.ScatterGraph2DView" label="Scatter Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-scatter-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.ScatterGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxhFLBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.BubbleGraph2DView" label="Bubble Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-bubble-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.BubbleGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxhVLBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.IntensityGraph2DView" label="Intensity Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-intensity-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.IntensityGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxhlLBEfCFTbvfeawKYQ" elementId="org.csstudio.graphene.MultiAxisLineGraph2DView" label="Multi-Axis Line Graph" iconURI="platform:/plugin/org.csstudio.graphene/icons/graphene-multiaxis-16.png" tooltip="" category="Graphene" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.graphene.MultiAxisLineGraph2DView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.graphene"/>
    <tags>View</tags>
    <tags>categoryTag:Graphene</tags>
  </descriptors>
  <descriptors xmi:id="_refxh1LBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.CreateLogEntry" label="Create Log Entry" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logentry-add-16.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.CreateLogEntryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxiFLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.LogTableView" label="Log Table" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logbook-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.LogTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxiVLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.LogTreeView" label="Log Tree" iconURI="platform:/plugin/org.csstudio.logbook.ui/icons/logbook-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logbook.ui.LogTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logbook.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxilLBEfCFTbvfeawKYQ" elementId="org.csstudio.logging.ui.LoggingConfiguration" label="Logging Configuration" iconURI="platform:/plugin/org.csstudio.logging.ui/icons/utilities-log-viewer-16.png" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logging.ui.LoggingConfiguration"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logging.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_refxi1LBEfCFTbvfeawKYQ" elementId="org.csstudio.logging.ui.FXLoggingConfiguration" label="Logging Configuration" iconURI="platform:/plugin/org.csstudio.logging.ui/icons/utilities-log-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.logging.ui.FXLogginConfiguration"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.logging.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxjFLBEfCFTbvfeawKYQ" elementId="org.csstudio.security.info" label="Security Info" iconURI="platform:/plugin/org.csstudio.security.ui/icons/logout.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.security.ui.internal.SecurityInfoView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.security.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxjVLBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.ui.CreateShift" label="Sign off" iconURI="platform:/plugin/org.csstudio.shift.ui/icons/SignOff-icon.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.shift.ui.CreateShiftView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.shift.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxjlLBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.ui.ShiftTableView" label="Shift Table" iconURI="platform:/plugin/org.csstudio.shift.ui/icons/shift-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.shift.ui.ShiftTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.shift.viewer"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxj1LBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.archiveview.ArchiveView" label="Archive Search" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/search.gif" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.search.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxkFLBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.sample_view" label="Inspect Samples" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/inspect.gif" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.sampleview.SampleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxkVLBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.exportview.ExportView" label="Export Samples" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/export.png" tooltip="" allowMultiple="true" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.exportview.ExportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxklLBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.waveformview.WaveformView" label="Inspect Waveforms" iconURI="platform:/plugin/org.csstudio.trends.databrowser2/icons/wavesample.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.trends.databrowser2.waveformview.WaveformView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.trends.databrowser2"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxk1LBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.adlParser.ADLTreeView" label="ADL Tree View" iconURI="platform:/plugin/org.csstudio.utility.adlParser/icons/medm.JPG" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.adlparser.ADLTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.adlParser"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxlFLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.clock.view" label="Clock" iconURI="platform:/plugin/org.csstudio.utility.clock/icons/clock.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.csstudio.utility.clock/org.csstudio.utility.clock.ClockView">
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxlVLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.eliza.ElizaView" label="Therapist" iconURI="platform:/plugin/org.csstudio.utility.eliza/icons/eliza.png" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.eliza.ElizaView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.eliza"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxllLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.ToolboxView" label="Datasources" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/datasource-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.DataSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxl1LBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.ServicesView" label="Services" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/service-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.ServicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxmFLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.pvmanager.ui.toolbox.FunctionsView" label="Formula Functions" iconURI="platform:/plugin/org.csstudio.utility.pvmanager.ui.toolbox/icons/formula-viewer-16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.pvmanager.ui.toolbox.FunctionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.pvmanager.ui.toolbox"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxmVLBEfCFTbvfeawKYQ" elementId="screenshotView" label="Screenshot" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.screenshot.view.ScreenshotView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.screenshot"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_refxmlLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.sysmon.SysMonView" label="System Monitor" iconURI="platform:/plugin/org.csstudio.utility.sysmon/icons/sysmon.gif" tooltip="" category="CSS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.utility.sysmon.SysMonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.utility.sysmon"/>
    <tags>View</tags>
    <tags>categoryTag:CSS</tags>
  </descriptors>
  <descriptors xmi:id="_refxm1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxnFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxnVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxnlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxn1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxoFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxoVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_refxolLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_refxo1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_refxpFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_refxpVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_refxplLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_refxp1LBEfCFTbvfeawKYQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxqFLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_refxqVLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search.internal.ui.SearchResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxqlLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxq1LBEfCFTbvfeawKYQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_refxrFLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_refxrVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxrlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxr1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_refxsFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxsVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxslLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxs1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxtFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxtVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxtlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxt1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxuFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxuVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxulLBEfCFTbvfeawKYQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxu1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_refxvFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView" label="OPI View" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxvVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT" label="OPI View (Left)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxvlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT" label="OPI View (Right)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxv1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP" label="OPI View (Top)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxwFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM" label="OPI View (Bottom)" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxwVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.placeHolder" label="OPI Placeholder" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" allowMultiple="true" category="BOY" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.PlaceHolderView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:BOY</tags>
  </descriptors>
  <descriptors xmi:id="_refxwlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiShellSummary" label="Standalone OPI Summary" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIRunner.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.csstudio.opibuilder.runmode.OPIShellSummary"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.csstudio.opibuilder"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <trimContributions xmi:id="_regaV1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_regaWFLBEfCFTbvfeawKYQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_regaWVLBEfCFTbvfeawKYQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_regaWlLBEfCFTbvfeawKYQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <snippets xsi:type="advanced:Perspective" xmi:id="_regaZFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opieditor.&lt;OPI Editor>" selectedElement="_regaZVLBEfCFTbvfeawKYQ" label="&lt;OPI Editor>" iconURI="platform:/plugin/org.csstudio.opibuilder.editor/icons/OPIBuilder.png">
    <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
    <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
    <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
    <tags>persp.actionSet:ScreenshotActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.help.ui.HelpView</tags>
    <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newOPIWizard</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newJSWizard</tags>
    <tags>persp.newWizSC:org.csstudio.opibuilder.wizards.newPyWizard</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_regaZVLBEfCFTbvfeawKYQ" selectedElement="_regabFLBEfCFTbvfeawKYQ" horizontal="true">
      <children xsi:type="basic:PartSashContainer" xmi:id="_regaZlLBEfCFTbvfeawKYQ" containerData="2000" selectedElement="_regaZ1LBEfCFTbvfeawKYQ">
        <children xsi:type="basic:PartStack" xmi:id="_regaZ1LBEfCFTbvfeawKYQ" elementId="left" containerData="3591" selectedElement="_regaaFLBEfCFTbvfeawKYQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_regaaFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_regaaVLBEfCFTbvfeawKYQ" elementId="leftBottom" containerData="6409" selectedElement="_regaalLBEfCFTbvfeawKYQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_regaalLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ContentOutline" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
          <children xsi:type="advanced:Placeholder" xmi:id="_regaa1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_regabFLBEfCFTbvfeawKYQ" containerData="8000" selectedElement="_regabVLBEfCFTbvfeawKYQ" horizontal="true">
        <children xsi:type="basic:PartSashContainer" xmi:id="_regabVLBEfCFTbvfeawKYQ" containerData="6876" selectedElement="_regablLBEfCFTbvfeawKYQ">
          <children xsi:type="advanced:Placeholder" xmi:id="_regablLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" containerData="7500"/>
          <children xsi:type="basic:PartStack" xmi:id="_regab1LBEfCFTbvfeawKYQ" elementId="bottom" containerData="2500" selectedElement="_regaclLBEfCFTbvfeawKYQ">
            <children xsi:type="advanced:Placeholder" xmi:id="_regacFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
            <children xsi:type="advanced:Placeholder" xmi:id="_regacVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
            <children xsi:type="advanced:Placeholder" xmi:id="_regaclLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ProblemView" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:General</tags>
            </children>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_regac1LBEfCFTbvfeawKYQ" elementId="right" containerData="3124" selectedElement="_regadFLBEfCFTbvfeawKYQ">
          <tags>BOY</tags>
          <children xsi:type="advanced:Placeholder" xmi:id="_regadFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.PropertySheet" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:General</tags>
          </children>
          <children xsi:type="advanced:Placeholder" xmi:id="_regadVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:cdb8bf80-0c1a-402c-abe3-0daab0c8ce25" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:BOY</tags>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <snippets xsi:type="advanced:Perspective" xmi:id="_regadlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" selectedElement="_regad1LBEfCFTbvfeawKYQ" label="&lt;OPI Runtime>" iconURI="platform:/plugin/org.csstudio.opibuilder/icons/OPIBuilder.png">
    <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
    <tags>persp.actionSet:org.csstudio.opibuilder.actionSet</tags>
    <tags>persp.actionSet:org.csstudio.opibuilder.editor.actionSet</tags>
    <tags>persp.actionSet:ScreenshotActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_regad1LBEfCFTbvfeawKYQ" selectedElement="_regae1LBEfCFTbvfeawKYQ" horizontal="true">
      <children xsi:type="basic:PartStack" xmi:id="_regaeFLBEfCFTbvfeawKYQ" elementId="LEFT" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_regaeVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewLEFT:*" toBeRendered="false" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:BOY</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_regaelLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_regae1LBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_regafFLBEfCFTbvfeawKYQ" horizontal="true">
        <children xsi:type="basic:PartSashContainer" xmi:id="_regafFLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_regaf1LBEfCFTbvfeawKYQ">
          <children xsi:type="basic:PartStack" xmi:id="_regafVLBEfCFTbvfeawKYQ" elementId="TOP" toBeRendered="false" containerData="2500">
            <children xsi:type="advanced:Placeholder" xmi:id="_regaflLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewTOP:*" toBeRendered="false" closeable="true">
              <tags>View</tags>
              <tags>categoryTag:BOY</tags>
            </children>
          </children>
          <children xsi:type="basic:PartSashContainer" xmi:id="_regaf1LBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_regagFLBEfCFTbvfeawKYQ">
            <children xsi:type="basic:PartSashContainer" xmi:id="_regagFLBEfCFTbvfeawKYQ" containerData="7500" selectedElement="_regaglLBEfCFTbvfeawKYQ" horizontal="true">
              <children xsi:type="advanced:Placeholder" xmi:id="_regagVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="5000"/>
              <children xsi:type="basic:PartStack" xmi:id="_regaglLBEfCFTbvfeawKYQ" elementId="DEFAULT_VIEW" containerData="5000" selectedElement="_regah1LBEfCFTbvfeawKYQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_regag1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:9bfdc6ba-7fc6-4cc0-a0fc-5d08bad77174" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_regahFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:*" toBeRendered="false" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_regahVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e1315108-25e1-4c24-9f0b-fcd8bfe77261" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_regahlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:e5f69e38-9150-4898-960d-c2875a5606ab" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_regah1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiView:654bd0e8-d777-4c1b-a809-6fc1e5ac144a" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:BOY</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_regaiFLBEfCFTbvfeawKYQ" elementId="BOTTOM" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_regaiVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewBOTTOM:*" toBeRendered="false" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:BOY</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_regailLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_regai1LBEfCFTbvfeawKYQ" elementId="RIGHT" toBeRendered="false" containerData="2500">
          <children xsi:type="advanced:Placeholder" xmi:id="_regajFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiViewRIGHT:*" toBeRendered="false" closeable="true">
            <tags>View</tags>
            <tags>categoryTag:BOY</tags>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <commands xmi:id="_regajVLBEfCFTbvfeawKYQ" elementId="org.csstudio.ui.menu.app.restart" commandName="Restart CS-Studio" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regajlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaj1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regakFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_reg_sVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regakVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_regaklLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regak1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regalFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.commands.editembeddedopi" commandName="Edit Embedded OPI" description="Open embedded OPI in editor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regalVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regallLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regal1LBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.epics.pvtree.OpenPVTree" commandName="EPICS PV Tree" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regamFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regamVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regamlLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmTable" commandName="AlarmTable" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regam1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_reg_sFLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reganFLBEfCFTbvfeawKYQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_reganVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reganlLBEfCFTbvfeawKYQ" elementId="org.csstudio.security.logout" commandName="Log out" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regan1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaoFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaoVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaolLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regao1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regapFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regapVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaplLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regap1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaqFLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_reg_qVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaqVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaqlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaq1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regarFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regarVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regarlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regar1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regasFLBEfCFTbvfeawKYQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regasVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaslLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regas1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regatFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regatVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regatlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regat1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regauFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regauVLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.probe.OpenPhoebusProbe" commandName="Probe" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaulLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regau1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regavFLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regavVLBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.viewer.OpenShiftViewer" commandName="Search Shift" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regavlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regav1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regawFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regawVLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenWaterfall" commandName="Waterfall" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regawlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaw1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaxFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaxVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaxlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.runopi" commandName="Run OPI" description="Run the OPI file currently in the editor" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regax1LBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.opibuilder.OPIEditor" commandName="OPI editor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regayFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regayVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaylLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.commands.editopi" commandName="Edit OPI" description="Open current OPI in editor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regay1LBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.addProperty" commandName="Add Property" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regazFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regazVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regazlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regaz1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega0FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega0VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega0lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega01LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega1FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega1VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega1lLBEfCFTbvfeawKYQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_reg_r1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega11LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega2FLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.exportlogs" commandName="Export Logs" description="Export the selected logs to a .csv or .txt file" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega2VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega2lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega21LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.samples.install" commandName="Install Examples" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega3FLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega3VLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusChannelTable" commandName="ChannelTable" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega3lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega31LBEfCFTbvfeawKYQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_reg_qlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega4FLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega4VLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega4lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega41LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega5FLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega5VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega5lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega51LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.STEP_FRONT" commandName="Step Front" description="Change the widget order a step front" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega6FLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega6VLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega6lLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega61LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega7FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega7VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega7lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega71LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega8FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega8VLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.pvmanager.probe.OpenProbe" commandName="Probe" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega8lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega81LBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmTree" commandName="AlarmTree" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega9FLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega9VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega9lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega91LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega-FLBEfCFTbvfeawKYQ" elementId="org.cstudio.perspectives.LoadPerspectives" commandName="Load Perspectives" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega-VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega-lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega-1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega_FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega_VLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.removeTag" commandName="Remove Tag" description="Remove tag from the selected channels" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega_lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_rega_1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbAFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbAVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbAlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbA1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_reg_qVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbBFLBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.ui.OpenShiftBuilderDialog" commandName="Start Shift" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbBVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbBlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbB1LBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.opibuilder.OPIRuntime" commandName="OPI runtime" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbCFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbCVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbClLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbC1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbDFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbDVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbDlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbD1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbEFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbEVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_reg_sVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbElLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_regbE1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbFFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbFVLBEfCFTbvfeawKYQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbFlLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_reg_nlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbF1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbGFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbGVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbGlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbG1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbHFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbHVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbHlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbH1LBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbIFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbIVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbIlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbI1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbJFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbJVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbJlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbJ1LBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.removeProperty" commandName="Remove property" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbKFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbKVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbKlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbK1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbLFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_reg_n1LBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbLVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_regbLlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_regbL1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_regbMFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbMVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbMlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_reg_q1LBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbM1LBEfCFTbvfeawKYQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_regbNFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbNVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbNlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbN1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbOFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbOVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_reg_rVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbOlLBEfCFTbvfeawKYQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_regbO1LBEfCFTbvfeawKYQ" elementId="org.csstudio.ui.menu.app.switch_workspace" commandName="Switch Workspace..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbPFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbPVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbPlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Contextual Help" description="Open the contextual help" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbP1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbQFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbQVLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.modifychannel" commandName="Modify Channel" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbQlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbQ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_reg_slLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbRFLBEfCFTbvfeawKYQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_regbRVLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenChannelTreeByProperty" commandName="Channel Tree by Property" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbRlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbR1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbSFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbSVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbSlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbS1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbTFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbTVLBEfCFTbvfeawKYQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbTlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbT1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbUFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbUVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbUlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbU1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbVFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbVVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbVlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbV1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbWFLBEfCFTbvfeawKYQ" elementId="org.csstudio.diag.postanalyser.open" commandName="Post Analyzer" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbWVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbWlLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbW1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbXFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbXVLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbXlLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.OpenLogEntryUpdateDialog" commandName="Reply" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbX1LBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenChannelLinePlot" commandName="Channel Line Plot" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbYFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbYVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbYlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbY1LBEfCFTbvfeawKYQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbZFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbZVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbZlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbZ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbaFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbaVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbalLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regba1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbbFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_reg_slLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbbVLBEfCFTbvfeawKYQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_regbblLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbb1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbcFLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.addTag" commandName="Add Tag" description="Tag the selected channels" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbcVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbclLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbc1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbdFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbdVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbdlLBEfCFTbvfeawKYQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbd1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbeFLBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.ui.EndShiftBuilder" commandName="End Shift" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbeVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbelLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbe1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbfFLBEfCFTbvfeawKYQ" elementId="org.csstudio.pretune.new" commandName="New pretune" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbfVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_reg_sVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbflLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_regbf1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbgFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbgVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbglLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbg1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.STEP_BACK" commandName="Step Back" description="Change the widget order a step back" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbhFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbhVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbhlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbh1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbiFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbiVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbilLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbi1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.TO_BACK" commandName="To Back" description="Change the widget order to back" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbjFLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbjVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.layoutWidgets" commandName="Layout Widgets" description="Layout Widgets" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbjlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbj1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.compactMode" commandName="Compact Mode" description="Enter/Exit compact mode" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbkFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbkVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_reg_qlLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbklLBEfCFTbvfeawKYQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_regbk1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regblFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.openProbeOPI" commandName="OPI Probe" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regblVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbllLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbl1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbmFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_reg_sFLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbmVLBEfCFTbvfeawKYQ" elementId="url" name="URL"/>
    <parameters xmi:id="_regbmlLBEfCFTbvfeawKYQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_regbm1LBEfCFTbvfeawKYQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_regbnFLBEfCFTbvfeawKYQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_regbnVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbnlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbn1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regboFLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusEmail" commandName="EmailLabel" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regboVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbolLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbo1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbpFLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.copyurltoclipboard" commandName="Copy URL to Clipboard" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbpVLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.pvtable.OpenPhoebusPVTable" commandName="PV Table" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbplLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbp1LBEfCFTbvfeawKYQ" elementId="org.csstudio.shift.ui.CloseShiftBuilder" commandName="Close Shift" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbqFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_reg_sFLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbqVLBEfCFTbvfeawKYQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_regbqlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbq1LBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbrFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbrVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbrlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbr1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbsFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbsVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbslLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbs1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbtFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbtVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbtlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbt1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbuFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbuVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.fullscreen" commandName="Full Screen" description="Enter/Exit Full Screen" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbulLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbu1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbvFLBEfCFTbvfeawKYQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbvVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbvlLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbv1LBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.viewer.OpenLogViewer" commandName="View Details" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbwFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbwVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbwlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbw1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Rebase Interactive" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbxFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_reg_qVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regbxVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_regbxlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_regbx1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbyFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbyVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbylLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regby1LBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.OpenDataBrowserPopup" commandName="Data Browser" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbzFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbzVLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.pvtree.OpenPhoebusPVTree" commandName="PV Tree" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbzlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regbz1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb0FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb0VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb0lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb01LBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb1FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb1VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb1lLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb11LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb2FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb2VLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenChannelViewer" commandName="Channel Viewer" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb2lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb21LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb3FLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb3VLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb3lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb31LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb4FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb4VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb4lLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.actions.OpenPhoebusAlarmPanel" commandName="Alarm Panel" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb41LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_reg_rVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regb5FLBEfCFTbvfeawKYQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_regb5VLBEfCFTbvfeawKYQ" elementId="org.csstudio.security.login" commandName="Log in" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb5lLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb51LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb6FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_reg_qlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb6VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb6lLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.olog.properties.reviewsign" commandName="Review and Sign" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb61LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb7FLBEfCFTbvfeawKYQ" elementId="org.csstudio.trends.databrowser.NewDataBrowserHandler" commandName="Data Browser" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb7VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb7lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb71LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb8FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb8VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb8lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb81LBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_reg_olLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb9FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb9VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb9lLBEfCFTbvfeawKYQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_reg_rFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb91LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb-FLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb-VLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb-lLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenPVTableByProperty" commandName="PV Table by Property" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb-1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb_FLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb_VLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb_lLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regb_1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_reg_plLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regcAFLBEfCFTbvfeawKYQ" elementId="title" name="Title"/>
    <parameters xmi:id="_regcAVLBEfCFTbvfeawKYQ" elementId="message" name="Message"/>
    <parameters xmi:id="_regcAlLBEfCFTbvfeawKYQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_regcA1LBEfCFTbvfeawKYQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_regcBFLBEfCFTbvfeawKYQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_regcBVLBEfCFTbvfeawKYQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_regcBlLBEfCFTbvfeawKYQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_regcB1LBEfCFTbvfeawKYQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_regcCFLBEfCFTbvfeawKYQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_regcCVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcClLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcC1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcDFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcDVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcDlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcD1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcEFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.opiShellsChanged" commandName="OPI Shell opened" description="OPI Shell opened" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcEVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcElLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcE1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcFFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcFVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcFlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcF1LBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcGFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.commands.openopiprobe" commandName="Open OPI Probe" description="Send PV name to OPI Probe" category="_reg_n1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcGVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcGlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcG1LBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.pvmanager.widgets.CopyValueToClipboardHandler" commandName="Copy Value to Clipboard" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcHFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcHVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcHlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_reg_q1LBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regcH1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_regcIFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcIVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcIlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_reg_sVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regcI1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_regcJFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_regcJVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcJlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcJ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcKFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcKVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcKlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcK1LBEfCFTbvfeawKYQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_reg_rFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcLFLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.adl2boy.writeColorMapToDefaultFile" commandName="Write ADL Color Map to Color Map File" description="Write the ADLs color Map to Boys Defaults" category="_reg_p1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcLVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcLlLBEfCFTbvfeawKYQ" elementId="org.csstudio.utility.channel.info" commandName="Show Channel Info" description="Show details for the selected channels" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcL1LBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_reg_nlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcMFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcMVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcMlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcM1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcNFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcNVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcNlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcN1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcOFLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_reg_nlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcOVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcOlLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.TO_FRONT" commandName="To Front" description="Change the widget order to front" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcO1LBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcPFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcPVLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.olog.property.shift.searchlogs" commandName="search shift logs" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcPlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcP1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcQFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_reg_rVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_regcQVLBEfCFTbvfeawKYQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_regcQlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcQ1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcRFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_regcRVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_EFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_EVLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.databrowser.OpenPhoebusDatabrowserEditor" commandName="Open Phoebus Databrowser Editor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_ElLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_E1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_FFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_FVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_FlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_F1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_GFLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_GVLBEfCFTbvfeawKYQ" elementId="org.csstudio.csdata.clipboard.copy_pv" commandName="Copy PV to clipboard" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_GlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_G1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_HFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_reg_oFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_HVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_HlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_H1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_IFLBEfCFTbvfeawKYQ" elementId="org.csstudio.logging.ui.RefreshLoggers" commandName="Refresh" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_IVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_IlLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenChannelOrchestrator" commandName="Channel Orchestrator" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_I1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_JFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_JVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_JlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_J1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_KFLBEfCFTbvfeawKYQ" elementId="org.csstudio.logbook.ui.OpenLogEntryBuilderDialog" commandName="Create Log Entry" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_KVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_KlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_K1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_LFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_LVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_LlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_L1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_MFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_reg_sFLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_MVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_reg_MlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_reg_slLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_M1LBEfCFTbvfeawKYQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_reg_NFLBEfCFTbvfeawKYQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_reg_NVLBEfCFTbvfeawKYQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_reg_NlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_N1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_reg_sFLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_OFLBEfCFTbvfeawKYQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_reg_OVLBEfCFTbvfeawKYQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_reg_OlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_O1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.adl2boy.editADLwithBoy" commandName="Convert ADL File to OPI" description="Convert ADL to OPI" category="_reg_p1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_PFLBEfCFTbvfeawKYQ" elementId="org.csstudio.ui.util.configure" commandName="Configure..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_PVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_reg_slLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_PlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_P1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_QFLBEfCFTbvfeawKYQ" elementId="org.csstudio.channel.views.OpenWaterfallQuery" commandName="Waterfall" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_QVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_QlLBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.databrowser.OpenPhoebusDatabrowser" commandName="Open Phoebus Databrowser" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_Q1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_RFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_RVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_reg_rVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_RlLBEfCFTbvfeawKYQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_reg_R1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_SFLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_SVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_reg_rVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_SlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_S1LBEfCFTbvfeawKYQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_TFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_TVLBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.actions.selectParent" commandName="Select Parent" description="Select Parent Container" category="_reg_oVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_TlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_T1LBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_UFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_UVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_reg_sVLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_UlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_reg_U1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_reg_q1LBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_VFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_reg_q1LBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_VVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_reg_VlLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_V1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_reg_plLBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_WFLBEfCFTbvfeawKYQ" elementId="title" name="Title"/>
    <parameters xmi:id="_reg_WVLBEfCFTbvfeawKYQ" elementId="message" name="Message"/>
    <parameters xmi:id="_reg_WlLBEfCFTbvfeawKYQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_reg_W1LBEfCFTbvfeawKYQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_reg_XFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_XVLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_XlLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_reg_pVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_X1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_YFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_YVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_YlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_reg_pFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_Y1LBEfCFTbvfeawKYQ" elementId="org.csstudio.phoebus.integration.Launch" commandName="Launch Phoebus" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_ZFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_reg_sFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_ZVLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_reg_qFLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_ZlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_reg_q1LBEfCFTbvfeawKYQ">
    <parameters xmi:id="_reg_Z1LBEfCFTbvfeawKYQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_reg_aFLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_reg_rlLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_aVLBEfCFTbvfeawKYQ" elementId="org.csstudio.security.ui.dummycommand" commandName="org.csstudio.security.ui.dummycommand"/>
  <commands xmi:id="_reg_alLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.csstudio.opibuilder.actionSet/org.csstudio.opibuilder.openTopFileAction" commandName="Top Files" description="Open top files in editor. e.g. Open OPIs in run mode" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_a1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::ScreenshotActionSet/OpenScreenshotWindow" commandName="Screenshot" description="Opens the screenshot window (Shortcut CTRL+ALT+S)." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_bFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_bVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_blLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_b1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_cFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_cVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_clLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_c1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_dFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_dVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_dlLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_d1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_eFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_eVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_elLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_e1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_fFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_fVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_flLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_f1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_gFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_gVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_glLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_g1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_hFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_hVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_hlLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_h1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_iFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_iVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_ilLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_i1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_jFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_jVLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_jlLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_j1LBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <commands xmi:id="_reg_kFLBEfCFTbvfeawKYQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_reg_sVLBEfCFTbvfeawKYQ"/>
  <addons xmi:id="_reg_kVLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_reg_klLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_reg_k1LBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_reg_lFLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_reg_lVLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_reg_llLBEfCFTbvfeawKYQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_reg_l1LBEfCFTbvfeawKYQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_reg_mFLBEfCFTbvfeawKYQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_reg_mVLBEfCFTbvfeawKYQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon">
    <persistedState key="org.csstudio.opibuilder.opieditor.&lt;OPI Editor>" value=""/>
    <persistedState key="org.csstudio.opibuilder.OPIRuntime.perspective.&lt;OPI Runtime>" value=""/>
  </addons>
  <addons xmi:id="_reg_mlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_reg_m1LBEfCFTbvfeawKYQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_reg_nFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_reg_nVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_reg_nlLBEfCFTbvfeawKYQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_reg_n1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_reg_oFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_reg_oVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_reg_olLBEfCFTbvfeawKYQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_reg_o1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_reg_pFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_reg_pVLBEfCFTbvfeawKYQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_reg_plLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_reg_p1LBEfCFTbvfeawKYQ" elementId="org.csstudio.opibuilder.adl2boy.BOYCategory" name="BOY" description="A category to hold references to boy commands"/>
  <categories xmi:id="_reg_qFLBEfCFTbvfeawKYQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_reg_qVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_reg_qlLBEfCFTbvfeawKYQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_reg_q1LBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_reg_rFLBEfCFTbvfeawKYQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_reg_rVLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_reg_rlLBEfCFTbvfeawKYQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_reg_r1LBEfCFTbvfeawKYQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_reg_sFLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_reg_sVLBEfCFTbvfeawKYQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_reg_slLBEfCFTbvfeawKYQ" elementId="org.eclipse.ui.category.help" name="Help"/>
</application:Application>
