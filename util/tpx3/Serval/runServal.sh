#!/bin/bash
# --resourcePoolSize=2097152. 524288/laptop, 262144 (2024-1-11), 1048576
# Serval 3.3: --resourcePoolSize=26214400
# java -jar serval-3.3.0.jar --httpPort=8081
# java -jar serval-3.3.0.jar --httpPort=8081 --resourcePoolSize=1048576

# Bogdan bl100-daq7
# /usr/bin/java -jar /opt/serval-3.3.0.jar --resourcepoolsize=3276800 --tcpIp=************** --httpPort=8080 --httpLog=/dev/null
#java -jar serval-3.3.2-SNAPSHOT.jar  --httpPort=8081 --resourcePoolSize=524288  
#java -jar serval-3.3.0.jar --httpPort=8081 

#java -jar serval-3.3.0.jar --httpPort=8081 --resourcePoolSize=2097152
#java -jar serval-3.3.0.jar --httpPort=8081 --tcpIp=127.0.0.1 --resourcePoolSize=2097152
#java -jar serval-3.3.0.jar --httpPort=8081 --tcpIp=127.0.0.1 --resourcePoolSize=3276800
#java -jar serval-3.3.0.jar --httpPort=8081 --tcpIp=127.0.0.1 --resourcePoolSize=1638400
# java -jar serval-3.3.0.jar --httpPort=8081 --tcpIp=127.0.0.1 

# One chip
#java -jar serval-3.3.2-SNAPSHOT.jar  --httpPort=8081 --resourcePoolSize=524288
#java -jar serval-4.1.0.jar  --httpPort=8081 --resourcePoolSize=524288
java -jar serval-4.1.1-rc1.jar  --httpPort=8081 --resourcePoolSize=524288

# 2 tpx3 emulators [emulate a 2x4 (8 chip) Timepix3 detector.]
#java -jar serval-3.3.2-SNAPSHOT.jar --experimental --httpPort=8081 --resourcePoolSize=524288

