Serval v4.1.1
----------------------------------------------
Minor release with small improvements

* MPX3 Equalisation Improvements (GEN2-351)

* TPX4 TDC processing (GEN2-466)

* Porting of the ASBI format from the latest Serval 3 version (GEN2-520, GEN2-358)

* Support any number of chips, in particular the new 2x4 chipboard (GEN2-529)

* User control over restarting TPX3 timers, avoiding unexpected changes in the TOA corrections (GEN2-545)

Bug fix
* Single image acquisition with "multiply correction" problem due to an empty resource pool (GEN2-547)

Serval v4.1.0
----------------------------------------------

This release introduces the support for the new Spidr3T board (GEN2-499), featuring full rate readout of 4 TPX3 chips,
not yet the full rate of 8 TPX3 chips. On the classic Spidr3 it requires a newer firmware (v0.3.0)

Major improvements:
* the sorting of TPX3 data is more robust (GEN2-496, GEN2-498)

Minor improvements:
* the Disruptor is used again on input packets, so fewer of these are lost (GEN2-492)
* the Disruptor is updated to version 4 (GEN2-497)
* a check is added for the firmware version of the Spidr3 board (GEN2-524)

Bug fixes:
* potential race conditions in the ResourcePool fixed (GEN2-504)
* when there is no BIAS board present, there would be many messages in the log file (GEN2-507)
* the resource pool would be allocated twice, possibly running out of memory (GEN2-490)

Serval v4.0.0
----------------------------------------------

This is a major release, it requires Java 17, and the API has changed a little, like providing more chip type information.

Major features:
* Initial support for TPX4, at low speed (1 link per half over the 10 Gbit/s connection) (GEN2-419)
* Jersey generated API, docs, client code (GEN2-435)
* 4D-STEM support (GEN2-399)

Minor improvements:
* Support for other (local) IP addresses in the tcp destination config (GEN2-288)
* the DVDD values are included in the DetectorHealth for MPX3 (GEN2-396)

Bugfix:
* The equalisation plot would show a high spike in the middle (GEN2-459)


Serval v3.3.1
----------------------------------------------

Features:
* Support for multiple modules with MPX3 (2 x 4 setup)
* Support for TPX3 probe card (GEN2-470)

Bugfix:
* Error when SplitStrategy is configured as SINGLE_FILE for tcp. (GEN2-471)

Serval v3.3.0
----------------------------------------------
Features:
* Added a per-pixel pixel configuration API (GEN2-453):
  ** PUT /detector/chips/<chip-id>/pixelconfig/<row>/<col> . This sets the pixel threshold value to the requested value. GET is also available to query the value for the pixel.
* (MPX3) Spectral mode
  ** For detector types that support it (MPX3 detectors with spectral sensors), Color mode can now be enabled in /detector/config to output 4/8 images for each pixel.
  ** Each image is 1/4th of the original image size, but each image can be configured using different thresholds.
* (MPX3) Binning (GEN2-356)
  ** To reduce data size of MPX3 images, images can be binned.
  ** Binning is configured in /measurement/config/processing/binning. This takes a "Size" parameter indicating the number of pixels to bin (in a square, e.g., Size = 2 results in a 2x2 binning).
  ** To enable the binning processing, the processing has to be enabled for the given channel in the /server/destination configuration.
    *** Due to this, it is possible to stream non-binned images as a preview stream, but save binned images to disk.
  ** Images reduce in width and height without increasing the dynamic range, thus the Size parameter is limited for a given PixelDepth.
  ** Binning causes more performance load.
* (TPX3) Multiple raw streams
  ** Multiple raw streams are now supported. This includes streaming raw data to TCP and writing it to disk at the same time.


Changes:
* The resource pool for TPX3 is now kept allocated during Serval's runtime. (GEN2-404)
  ** This means a higher memory usage while serval is idle, but faster measurement start and stop times.
  ** If preferred, the previous behaviour can be obtained by starting serval with --releaseResources.
* Serval no longer automatically generates a http.log file. This can still be enabled by providing the -httpLog=/some/file/here startup parameter.



Bugfixes:
* Fixed global timestamps causing dropped frames (GEN2-451)
* Fixed HTTP 500 error when uploading TimeOfFlight defaults. (GEN2-446)




Serval v3.2.0
----------------------------------------------
Features:
* (MPX3) Expose CounterSelect HDMI channels in /detector/config. Can be used to set the HDMI channel where the CounterSelect channel in CONTINUOUS mode is emitted.
* Serval now warns when the MTU is set incorrectly.
* Support for the 4x2 TPX3 detector.
* (TPX3) TDC1 and TDC2 statistics are now split up in all locations where it could be read as a single value before.


Fixes:
* Fixed temperature dropping by ~10 degrees in /detector/health during measurement.
* Fixed 1-bit mode for new MPX3 firmware.
* PixelEventRate and TDCEventRate are no longer calculated by extrapolation (for modes including exposure time). They now show the actual recorded value. This previously caused confusion on what the actual value should be.
  ** Unfortunately the rates for external trigger modes are still unreliable. This will be resolved in a future release.




Serval v3.1.1
----------------------------------------------
Fixes:
* Fixed being unable to enable sliding integration (when IntegrationSize > 1).
* Fixed Multiply correction finding an incorrect value when the source value exceeded signed range (e.g., 127+ in 8-bit modes)
* Fixed integration not properly working when 24-bit images (MPX3) or TOF images (TPX3) were enabled (issue introduced in v3.1.0).
* Fixed MPX3 24-bit mode images ignoring the upper 12-bit values (issue introduced in v3.1.0).
* Fixed TPX3 equalization not performing the right procedure for negative polarization.




Serval v3.1.0
----------------------------------------------
Features:
* Added Gap Filling
  ** /measurement/config/corrections now contains "Gapfill".
  ** You can set the Distance: how far the strategy will look outward from a source pixel to find gap pixels.
  ** You can set the Strategy:
      *** NEIGHBOUR: copies over the value of the neighbour into the gap pixel.
      *** SPLIT: spreads the value of the source pixel out over the source pixel and gap pixels.
  ** Applied before the multiply correction.
* (TPX3) Improved Time of Flight support
  ** /measurement/config now contains a TimeOfFlight configuration, where TdcReference, Min and Max can be set.
      *** TdcReference is in the same format as Tdc in /detector/config: [PN01234]*
      *** Min / max define the TOF interval of events to consider for the image that Serval outputs.
  ** Histogram metadata now contains more fields:
      *** 'countPixels': The number of events.
      *** 'countNoTdc': The number of events that were registered before a TDC had arrived.
      *** 'countSum': The sum of events in the bins.
      *** 'countBelowMinimum': The number of events that were found to have a TOF value lower than the Min defined in the TimeOfFlight configuration.
      *** 'countAboveMaximum': the number of events that were found to have a TOF value higher than the Max defined in the TimeOfFlight configuration.
* /config/load?format=dacs now allows loading .json dac files.


New firmware support. Requires updating firmware to version 22083000 or later:
* (TPX3) Added sequential readout mode:
  ** Added 'count_fb' mode as a new "Mode" option in the Destination.
  ** Enabling 'count_fb' configures the sequential readout mode.
  ** This mode allows for higher count rates, at the cost of a higher deadtime and the loss of timing information.
  ** 'tot', 'toa' and 'tof' cannot be supported in this mode.
  ** Raw data format of the pixel events will be different in this mode.
* Sequence headers:
  ** Allows for better checking of data integrity.
  ** Reports when data is missing from the detector, and the frame is discarded.
      *** (TPX3) All raw data is kept, albeit incomplete.
  ** (TPX3) A new 64-bit word can be found at the start of a packet in the data, headered by 0x50.
* (TPX3) Shutter timestamps:
  ** Shutter timestamps are now sent out whenever the shutter opens or closes.
  ** Shutter timestamps are available in the raw data. Headered by 0x5f (open) and 0x5a (close). See the documentation for details.
* (TPX3) Heartbeat:
  ** Heartbeat timestamp are periodically sent out (depending on the trigger period).
  ** Heartbeat timestamps are available in the raw data. Headered by 0x5c. See the documentation for details.
* (TPX3) Improved stability in CONTINUOUS mode when little to no data is coming in.


Bugfixes:
* Histogram metadata sent over jsonimage should now have correct values of 'binSize', 'binWidth' and 'binOffset'.
* Time of flight is now correctly calculated when a frame's first event is not a TDC.




Serval v3.0.1
----------------------------------------------
Bugfixes:
* Fixed external trigger still causing TPX3 events with unexpected timestamps at the start next acquisition when not explicitly stopping the measurement.




Serval v3.0.0    (! API breaking changes !)
----------------------------------------------
API BREAKING changes:
* /detector/layout had its JSON representation changed.
* /detector/config now no longer contains the field "DetectorOrientation"
  ** This was moved to /detector/layout.
* Required deadtime for MPX3 is now 1ms for BothCounters and 24-bit modes, from 0.5ms.
  ** In previous versions, 0.5ms also failed to work in both modes, but now Serval enforces it for proper data acquisition.
  ** Deadtimes for 1-bit and 6-bit modes are more lenient, allowing for higher frame rates in AUTOTRIGSTART_TIMERSTOP mode.
* In /detector/info, ChipboardId was moved to the Boards structure.


Features:
* Added Megapixel support
* Added Grid Alignment
  ** /detector/layout is now PUT-able, which allows for placement of chips on specific coordinates.
* Added Corrections framework
  ** /measurement/config now contains a Corrections configuration.
  ** Image channels in the /server/destination now contain a 'Corrections' field, selecting for which corrections should be enabled.
  ** The jsonimage structure now contains the applied corrections in the header.
* Added Multiply correction:
  ** Pixel-wise multiplication factor of the output image.
* For MPX3, ChargeSumming can now be enabled in /detector/config.
* New logging format
* Added new startup arguments:
  ** --correctionHandlers sets the number of correction handler threads to use to correct images in parallel
  ** --tcpIp: Sets the ip address that Serval searches the detector on.
  ** --tcpPort: Sets the port Serval searches the detector on.


Bugfixes:
* Fixed external trigger being able to start before the timers had been reset, causing TPX3 events with unexpected timestamps.
* Improved stability of the TPX3CAM startup phase.
* Fixed TPX3 raw-mode SplitStrategy: "FRAME" sometimes throwing a NullPointerException.
* Fixed Charge-summing mode




Serval v2.3.6
----------------------------------------------
Changes:
* Added default value information to --help


Bugfixes:
* Fixed an issue where when sending an image over HTTP failed, a "StreamException" was thrown instead of the original exception.
  ** Whenever an issue occurs, the API should now return the correct error code 500, instead of 200.
* Fixed /measurement/stop not awaiting the pipeline stopping if it was already in the process of stopping.
* Fixed CONTINUOUS trigger mode sometimes submitting more frames than requested, resulting in "strange frameid" messages.
* Fixed certain API end-points not sending the appropriate content type header.
* Fixed a potential deadlock while stopping when running with small QueueSize destinations.




Serval v2.3.5
----------------------------------------------
Changes:
* jsonimage now always sends with a fixed image size for a given PixelDepth (MPX3) or mode (TPX3).
  ** Previous behaviour was a dynamic size, it is still recommended to read out the 'bitDepth' in the header.
  ** The sizes for MPX3 are:
    *** For 1 and 6 bit modes, Serval will send images of size 8 (1 byte per pixel).
    *** For 12 bit mode, Serval will send images of size 16.
    *** For 24 bit mode, Serval will send images of size 32.
  ** The sizes for TPX3 are:
    *** For ToT, ToA and count, Serval will send images of size 16.
    *** For ToF (TPX3), Serval will send images of size 32.


Bugfixes:
* Fixed slow jsonimage transfer due to excessive copying.
* Fixed invalid data on raw stream when queue is full.




Serval v2.3.4
----------------------------------------------
Features:
* Added for TPX3 an 'ExternalReferenceClock' option to the /detector/config.
    ** This allows to use an external clock as the clock for the detector. If you are interested in how to use this, please contact support.
    ** Turning on this setting requires that the detector already has an external clock connected through our trigger box.




Serval v2.3.3
----------------------------------------------
Features:
* Make iDelayConfig settable in the MPX3 detector config (recommended advanced use only).


Bugfixes:
* Raw over tcp would not close the socket after stopping the measurement.




Serval v2.3.2
----------------------------------------------
Bugfixes:
* Fixed Raw channel over TCP causing 'java.lang.IllegalArgumentException: URI scheme is not "file"' when starting a measurement.
* Tdc could show 'null' when unset. Now defaults to on: ["PN0123", "PN0123"] instead (as per documentation).
    ** This means that it should be explicitly turned off (if desired) by setting ["", ""].




Serval v2.3.1
----------------------------------------------
Bugfixes:
* MPX3 would eventually run out of resources after dropping many frames.




Serval v2.3.0
----------------------------------------------
Features:
* Re-enabled TPX3 support.
  ** TPX3 now benefits from the same features, bugfixes and performance improvements as MPX3.
  ** Added more diagnostics for missing TPX3 data.

* Added TdcEventRate to /dashboard
  ** TdcEventRate and PixelEventRate are now always the number of events that you would get if you opened the shutter for the entire second.
  ** In other words, EventRate = numEventsInLastSecond / openTimeInLastSecond

* Added tdcEventNumber and pixelEventNumber to the frame metadata (json header in jsonimage mode).
  ** Values correspond with the number of tdc events and pixel events in the frame.

* Added more tuning flags for the pipeline. The default values will often be sufficient.
  ** --frameAssemblers specifies the number of frame assemblers to use. Each frameAssembler will use one CPU core to build frames in parallel for high frame- or datarates.
  ** --ringBufferSize specifies the size of the ringbuffer in the pipeline.
  ** --networkBufferSize specifies the requested size of the NIC buffer.
  ** --fileWriters specifies the number of parallel file writers to use to write images to disk.
  ** --resourcePoolSize specifies the size of the resource pool Serval uses to accept packets from the detector.
      *** The default value should be enough for most usages of TPX3 Serval. Serval will inform you when increasing this value is needed.
  ** Flag --packetBuffers is now removed because it is no longer used.

* Added 'connect' TCP mode for MPX3 (see changelist of v2.1.5).


Bugfixes:
* Fixed Serval being unable to start if the detector was in an invalid settings state. Now reverts to default settings.




Serval v2.2.3    (! API breaking changes !)
----------------------------------------------
API BREAKING changes:
* A PixelDepth of 24 now requires BothCounters to be false instead of true.

Features:
* Enabled BothCounters
  ** Now supports 2x1, 2x6, 2x12 readout by setting 'BothCounters' in /detector/config to 'true', and setting PixelDepth accordingly.
  ** Channels in /server/destination now contain an extra field: 'Thresholds', which can be used to send specific thresholds to specific channels.
      *** If one channel contains multiple thresholds, thresholds are sent in natural order: 0 -> 1 -> 2 -> 3 -> 4 -> 5 -> 6 -> 7.
      *** This could mean, for a HTTP channel, that you have to perform multiple requests to get the images for a single trigger. For example the first one to get the threshold 0 image, and the second one to get the threshold 1 image.
  ** When using the 'jsonimage' mode, the JSON header metadata now contains a field 'thresholdID' which can be used to detect the threshold id of the image.


Bugfixes:
* 24 bit mode and Continuous mode used the wrong threshold (DAC Threshold[1] instead of Threshold[0]).




Serval v2.2.2
----------------------------------------------
Features:
* Added Software trigger
  ** New TriggerModes:
      *** SOFTWARESTART_SOFTWARESTOP: acquisition is started by software start trigger, stopped by software stop trigger.
      *** SOFTWARESTART_TIMERSTOP   : acquisition is started by software start trigger, stopped by HW timer.
  ** New API endpoints:
      *** /measurement/trigger/start : software trigger to start capturing one frame.
      *** /measurement/trigger/stop  : software trigger to stop capturing one frame.


Bugfixes:
* Fixed StopMeasurementOnDiskLimit not taking effect.




Serval v2.2.1
----------------------------------------------
Bugfixes:
* Fixed Windows support. Previously connecting with the detector would fail while an internet connection was active.




Serval v2.2.0 (MPX3-only)
----------------------------------------------
Features:
* Significantly improved performance
  ** On our tuned PCs, a frame rate of 2000 fps is achievable.

* Destination queues are now bounded, preventing sometimes unrecoverable OutOfMemoryErrors.
  ** The queue sizes are configurable by setting "QueueSize" to a custom value in a destination channel.
  ** The default queue sizes are 16 images for preview, and 1024 images for non-preview channels.
  ** When adding an item to a full queue, the following behaviour exists:
      *** Preview channels evict the oldest item if the queue is full.
          **** If you encounter this, you may want to increase the PreviewPeriod to reduce the number of frames being sampled.
      *** Non-preview channels wait until a spot in the queue is freed.
          **** If you encounter this, you may want to increase the TriggerPeriod to reduce the number of frames being processed.
      *** For both scenarios a message is displayed, at most once per second.

* 24 bit mode support.
  ** Can only output as jsonimage (TCP) or tiff (file). Both in 32 bit format.
  ** Integrated images (also in 1, 6, and 12 bit mode) can now also use the full 32 bit range.
      *** The previous cap of 65536 has been increased to 4294967296.
      *** As a consequence, integrated images can no longer be exported as PGM or PNG.

* Serval now checks for remaining Diskspace for file channels.
  ** If disk for a channel is almost full, measurement stops (by default, overridable for each channel by setting "StopMeasurementOnDiskLimit").
  ** If "StopMeasurementOnDiskLimit" is disabled for a channel, only that channel will stop writing to disk until enough space has been freed again.
  ** Remaining diskspace can be queried during a measurement through GET /dashboard.
  ** If a channel stops or continues writing, a Notification is sent, which can be queried through GET /dashboard.




Serval v2.1.6
----------------------------------------------
Bugfixes:
* Fixed bias voltage not being set and read out correctly for chipboard types 0x43.




Serval v2.1.5    (! API breaking changes !)
----------------------------------------------
API BREAKING changes:
* The default TCP 'connect mode' for Raw channels has been changed to 'listen mode'.
  ** This means that by default, now clients interfacing with Serval over the Raw channel must *connect* to Serval, instead of opening a socket themselves.
  ** To obtain the previous behaviour, change 'tcp://localhost:port' to 'tcp://connect@localhost:port'
     *** For example, if your current Raw specification is:
         { "Base": "tcp://127.0.0.1:8088" }, you should convert it to { "Base": "tcp://connect@127.0.0.1:8088" }, or update your client application.

Features:
* Added a 'connect mode' and 'listen mode' for TCP streams.
  ** In 'connect mode', Serval initiates the connection to the specified address and port.
  ** In 'listen mode', Serval listens at the specified port, waiting for a client to initiate the connection.
  ** 'Connect mode' is specified by prefixing the host with connect@.
    *** For example, 'tcp://connect@**************:8088' will make Serval connect to the specified ip address at the start of a measurement.
    *** The client must open a TCP socket at this address and port before starting a measurement.
  ** 'Listen mode' is specified by prefixing the host with listen@, or without prefix (default behaviour).
    *** For example, both 'tcp://localhost:8088' and 'tcp://listen@localhost:8088' result in Serval opening a TCP socket at port 8088 at the start of the measurement.
    *** The client can connect to this port after the measurement has started.

TPX-only features:
* Added an option to output the Raw file datastream in a single file.
  ** Under destination, Raw, 'SplitStrategy' now defines the way raw data is stored to disk.
  ** 'SplitStrategy' has two options: 'SINGLE_FILE' and 'FRAME'.
     *** 'SINGLE_FILE' writes the data stream to a single file (new behaviour).
     *** 'FRAME' writes the data for each frame to a separate file (previous behaviour).
  ** When left unspecified, 'SplitStrategy' defaults to 'FRAME'.

* Added GlobalTimestampInterval to the /detector/config.
  ** This interval specifies the interval in seconds with which the raw stream will contain 'GlobalTimestamp' events.
  ** By default, these events are emitted every 10 seconds.

Bugfixes:
* Fixed Windows support. Previously connecting with the detector would fail while an internet connection was active.
* Improved responsiveness of the /dashboard end-point. Previously this could in certain circumstances lead to a response time of up to 1s.
* TDC trigger number is now reset upon starting a new measurement.




Serval v2.1.4
+----------------------------------------------
Features:
* The HTTP server now starts before trying to connect with a detector, resulting in faster responsiveness.




Serval v2.1.3
----------------------------------------------
Features:
* Added program option --spidrNet to connect to a specific connected detector.
  ** The net is placed in the ip as such: 192.168.net.0
  ** Example: Set the value to 100 through 'java -jar serv-2.1.3.jar --spidrNet=100'




Serval v2.1.2
----------------------------------------------
Bugfixes:
* Fixed an issue when recording TOF values.
  The TOF values were set to the wrong unit (25ns instead of 1.56ns) when no TDC arrived
  earlier in the frame.




Serval v2.1.1
----------------------------------------------
Features:
* Rotation is now also supported on single-chip boards.
* Improved the speed of TIFF writing.
* Improved the speed of the detector/chips/<chip-id>/dacs end-point.
* Added 'ChainMode' to detector/config for detector chaining and synchronization.
  ** Using this functionality multiple detectors can be synchronized on the same clock
     and trigger at the same time. The LEADER synchronizes its clock and trigger to the
     FOLLOWERS.


Bugfixes:
* Fixed PUT detector/chips/<chip-id>/mask not working on MPX.
* Fixed GET detector/chip/<chip-id>/mask not checking for the right number of arguments.
  ** Feedback of this end-point has been improved as well.
* Fix TOF mode not working without a histogram channel.
* Flipping was applied incorrectly (orientation LEFT_MIRRORED was actually RIGHT_MIRRORED and vice versa).
* Rotating a mirrored image would rotate the wrong way (rotating right on UP_MIRRORED now yields LEFT_MIRRORED as expected).
* Fixed Dexter import only looking for mpx3.json while Dexter sometimes also exports the file as config.json.
* Fixed Dexter import misbehaving when operation mode is continuous and triggermode is autotrigger.
* Fixed Serval not working on boards without bias equipment.




Serval v2.1.0
----------------------------------------------
Features:
* Medipix3 support.
  ** 1, 6 and 12-bit-mode only.
  ** SRW and CRW both supported.
  ** 24-bit, colour, charge summing and double counter modes are not supported.
  ** [!] Performance is still somewhat limited.
    *** Writing images as PGM is fastest, other file formats do not reliably perform.
    *** Safe performance ranges are between 1-250 FPS (TriggerPeriod = 0.004).

* Enabled raw data sending over TCP.
* Diagnostics if the processing pipeline overflows.
  ** If you encounter a "Ringbuffer is full" message, Serval is unable to catch up with the incoming packets.
       You should reduce the ExposureTime and/or the TriggerPeriod to allow Serval some breathing room.

* Added DetectorOrientation to the Detector Configuration.
  ** Linked with the /detector/layout/rotate API.
  ** Settable; if set, the layout of the images rotates much like the rotate API.
  ** Can be used to query the current rotation state of the detector.


Bugfixes:
* Rounding on average integration mode was off, always rounding down.
* Writing PNG files failed when values got too high.
* In Continuous mode, ExposureTime and TriggerPeriod could differ, causing invalid states. Now, they must be equal, or one of them must be undefined.
* Duplicate file paths in destination are now allowed if the extension is different.
* Reconnections can no longer happen while already connecting.
* Preview always defaulted to a 30fps preview, even when not specified in the destination.
* When specifying a destination of a TCP channel that is never opened on the client-side, stopping the measurement deadlocks.
* Serval now uses less CPU resources while inactive.




Serval v2.0.1
----------------------------------------------
Features:
* Added support for 4x1 detectors.
* Added a rotation functionality.
  ** <host>/detector/layout/rotate
  ** rotation direction can be specified using direction=<direction>
     ** direction can be any of: 'right', 'left', '180'.
  ** layout can also be flipped by using flip=<flipdirection>
     ** flipdirection can be any of: 'horizontal', 'vertical'.
  ** Rotation always happens before the flip.

Bugfixes:
* Fixed an issue where a NullPointerException occurred on starting a measurement with only a Raw channel.


Serval v2.0.0
----------------------------------------------
First TPX3 release of Serval.
Consult the manual for an in-depth explanation of all the Serval features.

Features:
* Control your TPX3 cam using the new API.
  ** All HTTP end-points have changed since v1.x.x.
  ** Most JSON structures have a new format sine v1.x.x. Especially 'Destination' and 'Detector Config' (formerly SystemConfig).
  ** Consult the manual for the new end-points and JSON structures.

Additions since v1.0.1:
* Integration modes.
  ** 'sum' sums the pixel values of the images.
  ** 'average' averages the pixel values of the images.
  ** 'last' results in an image containing only the most recently recorded values of a pixel.
* Auto connect and reconnect.

Stability improvements and bugfixes:
* A number of issues relating to detector connectivity have been resolved.
* Improved stability by dealing with unexpected errors more gracefully.
* Serval now rejects any configurations putting it in an invalid or unsupported state.
